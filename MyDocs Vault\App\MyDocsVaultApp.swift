//
//  MyDocsVaultApp.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI

@main
struct MyDocsVaultApp: App {
    @StateObject private var authManager = AuthenticationManager()
    @StateObject private var documentManager = DocumentManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authManager)
                .environmentObject(documentManager)
                .onAppear {
                    // Authenticate when app launches
                    Task {
                        await authManager.authenticate()
                    }
                }
                .onChange(of: authManager.isAuthenticated) { isAuthenticated in
                    if !isAuthenticated {
                        // Re-authenticate when returning from background
                        Task {
                            await authManager.authenticate()
                        }
                    }
                }
        }
    }
}
