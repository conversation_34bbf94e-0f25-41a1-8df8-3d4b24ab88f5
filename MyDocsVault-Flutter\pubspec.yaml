name: mydocs_vault_flutter
description: A secure document storage app built with Flutter

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI Components
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.7296
  
  # Authentication
  local_auth: ^2.1.6
  
  # File Management
  file_picker: ^6.1.1
  image_picker: ^1.0.4
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  
  # Document Viewing
  flutter_pdfview: ^1.3.2
  photo_view: ^0.14.0
  
  # Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  
  # Utilities
  path: ^1.8.3
  uuid: ^4.1.0
  intl: ^0.18.1
  
  # Sharing
  share_plus: ^7.2.1
  
  # State Management
  provider: ^6.1.1
  
  # Navigation
  go_router: ^12.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
