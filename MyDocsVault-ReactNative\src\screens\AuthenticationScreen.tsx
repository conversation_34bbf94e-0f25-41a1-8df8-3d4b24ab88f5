import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  useColorScheme,
  Image,
} from 'react-native';
import {Button, ActivityIndicator} from 'react-native-paper';
import {AuthenticationService} from '../services/AuthenticationService';

interface Props {
  onAuthenticationSuccess: () => void;
}

const AuthenticationScreen: React.FC<Props> = ({onAuthenticationSuccess}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [isLoading, setIsLoading] = useState(false);
  const [biometryType, setBiometryType] = useState<string | null>(null);

  useEffect(() => {
    checkBiometryType();
  }, []);

  const checkBiometryType = async () => {
    try {
      const type = await AuthenticationService.getBiometryType();
      setBiometryType(type);
    } catch (error) {
      console.error('Error checking biometry type:', error);
    }
  };

  const handleAuthenticate = async () => {
    setIsLoading(true);
    
    try {
      const result = await AuthenticationService.authenticate();
      
      if (result.success) {
        onAuthenticationSuccess();
      } else {
        Alert.alert(
          'Authentication Failed',
          result.error || 'Please try again',
          [
            {
              text: 'Retry',
              onPress: handleAuthenticate,
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [
          {
            text: 'Retry',
            onPress: handleAuthenticate,
          },
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getBiometryIcon = () => {
    switch (biometryType) {
      case 'TouchID':
        return '👆';
      case 'FaceID':
        return '👤';
      case 'Biometrics':
        return '🔐';
      default:
        return '🔒';
    }
  };

  const getBiometryText = () => {
    switch (biometryType) {
      case 'TouchID':
        return 'Touch ID';
      case 'FaceID':
        return 'Face ID';
      case 'Biometrics':
        return 'Biometric';
      default:
        return 'Device';
    }
  };

  const styles = getStyles(isDarkMode);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* App Logo/Icon */}
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🗂️</Text>
          <Text style={styles.appTitle}>MyDocs Vault</Text>
          <Text style={styles.appSubtitle}>Secure Document Storage</Text>
        </View>

        {/* Authentication Section */}
        <View style={styles.authSection}>
          <View style={styles.biometryIconContainer}>
            <Text style={styles.biometryIcon}>{getBiometryIcon()}</Text>
          </View>
          
          <Text style={styles.authTitle}>
            Secure Access Required
          </Text>
          
          <Text style={styles.authDescription}>
            Use {getBiometryText()} authentication to access your secure documents
          </Text>

          <Button
            mode="contained"
            onPress={handleAuthenticate}
            disabled={isLoading}
            style={styles.authButton}
            contentStyle={styles.authButtonContent}
            labelStyle={styles.authButtonLabel}>
            {isLoading ? (
              <ActivityIndicator color="#ffffff" size="small" />
            ) : (
              `Authenticate with ${getBiometryText()}`
            )}
          </Button>
        </View>

        {/* Security Notice */}
        <View style={styles.securityNotice}>
          <Text style={styles.securityText}>
            🔒 Your documents are stored securely on this device and protected by biometric authentication
          </Text>
        </View>
      </View>
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 60,
    },
    logoIcon: {
      fontSize: 80,
      marginBottom: 16,
    },
    appTitle: {
      fontSize: 28,
      fontWeight: 'bold',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 8,
    },
    appSubtitle: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
    },
    authSection: {
      alignItems: 'center',
      width: '100%',
      marginBottom: 40,
    },
    biometryIconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: isDarkMode ? '#333333' : '#f0f0f0',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
    },
    biometryIcon: {
      fontSize: 40,
    },
    authTitle: {
      fontSize: 22,
      fontWeight: '600',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 12,
      textAlign: 'center',
    },
    authDescription: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
      marginBottom: 32,
      lineHeight: 22,
    },
    authButton: {
      backgroundColor: '#007AFF',
      borderRadius: 12,
      width: '100%',
    },
    authButtonContent: {
      paddingVertical: 8,
    },
    authButtonLabel: {
      fontSize: 16,
      fontWeight: '600',
    },
    securityNotice: {
      backgroundColor: isDarkMode ? '#1a1a1a' : '#f8f9fa',
      borderRadius: 12,
      padding: 16,
      width: '100%',
    },
    securityText: {
      fontSize: 14,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
      lineHeight: 20,
    },
  });

export default AuthenticationScreen;
