import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  useColorScheme,
  Alert,
} from 'react-native';
import {Card, Title, Paragraph, IconButton, ActivityIndicator} from 'react-native-paper';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';

import {Document, DocumentCategory, CATEGORY_INFO} from '../types/Document';
import {DocumentService} from '../services/DocumentService';
import {RootStackParamList} from '../../App';

type CategoryNavigationProp = StackNavigationProp<RootStackParamList, 'Category'>;
type CategoryRouteProp = RouteProp<RootStackParamList, 'Category'>;

interface Props {
  navigation: CategoryNavigationProp;
  route: CategoryRouteProp;
}

const CategoryScreen: React.FC<Props> = ({navigation, route}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const {category} = route.params;
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDocuments();
  }, [category]);

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const categoryDocuments = await DocumentService.getDocumentsByCategory(
        category as DocumentCategory,
      );
      setDocuments(categoryDocuments);
    } catch (error) {
      console.error('Error loading category documents:', error);
      Alert.alert('Error', 'Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentPress = (document: Document) => {
    navigation.navigate('DocumentDetail', {documentId: document.id});
  };

  const handleDeleteDocument = async (documentId: string) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await DocumentService.deleteDocument(documentId);
            if (success) {
              loadDocuments();
            } else {
              Alert.alert('Error', 'Failed to delete document');
            }
          },
        },
      ],
    );
  };

  const renderDocumentItem = ({item}: {item: Document}) => (
    <Card style={styles.documentCard} onPress={() => handleDocumentPress(item)}>
      <Card.Content>
        <View style={styles.cardContent}>
          <View style={styles.documentInfo}>
            <Title style={styles.documentTitle} numberOfLines={1}>
              {item.name}
            </Title>
            <Paragraph style={styles.documentSize}>
              {DocumentService.formatFileSize(item.fileSize)}
            </Paragraph>
            <Paragraph style={styles.documentDate}>
              {item.dateCreated.toLocaleDateString()}
            </Paragraph>
          </View>
          <IconButton
            icon="delete"
            size={20}
            onPress={() => handleDeleteDocument(item.id)}
          />
        </View>
      </Card.Content>
    </Card>
  );

  const categoryInfo = CATEGORY_INFO[category as DocumentCategory];
  const styles = getStyles(isDarkMode);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading documents...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Category Header */}
      <View style={styles.header}>
        <Text style={styles.categoryIcon}>{categoryInfo?.icon || '📁'}</Text>
        <Text style={styles.categoryTitle}>{category}</Text>
        <Text style={styles.documentCount}>
          {documents.length} {documents.length === 1 ? 'document' : 'documents'}
        </Text>
      </View>

      {/* Documents List */}
      {documents.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📄</Text>
          <Text style={styles.emptyStateTitle}>No Documents</Text>
          <Text style={styles.emptyStateDescription}>
            No documents found in this category.{'\n'}
            Add documents from the home screen.
          </Text>
        </View>
      ) : (
        <FlatList
          data={documents}
          renderItem={renderDocumentItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.documentsList}
        />
      )}
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 8,
      fontSize: 16,
      color: isDarkMode ? '#ffffff' : '#000000',
    },
    header: {
      alignItems: 'center',
      padding: 24,
      backgroundColor: isDarkMode ? '#1a1a1a' : '#f8f9fa',
    },
    categoryIcon: {
      fontSize: 48,
      marginBottom: 8,
    },
    categoryTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 4,
    },
    documentCount: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
    },
    documentsList: {
      padding: 16,
    },
    documentCard: {
      marginBottom: 12,
    },
    cardContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    documentInfo: {
      flex: 1,
    },
    documentTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    documentSize: {
      fontSize: 14,
      color: isDarkMode ? '#cccccc' : '#666666',
      marginBottom: 2,
    },
    documentDate: {
      fontSize: 12,
      color: isDarkMode ? '#aaaaaa' : '#888888',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyStateIcon: {
      fontSize: 64,
      marginBottom: 16,
    },
    emptyStateTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyStateDescription: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
      lineHeight: 22,
    },
  });

export default CategoryScreen;
