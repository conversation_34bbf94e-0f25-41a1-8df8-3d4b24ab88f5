# MyDocs Vault

A secure SwiftUI-based iOS app for storing and managing personal documents with biometric authentication.

## Features

### 🔐 Security
- **Face ID/Touch ID Authentication**: Secure biometric authentication on app launch and when returning from background
- **Local Storage**: All documents stored securely in the app's sandbox directory
- **Privacy-First**: No data leaves your device (except optional iCloud backup)

### 📄 Document Management
- **Multiple Import Sources**: 
  - Files app integration
  - Camera capture
  - Photo library selection
  - Text note creation
- **Supported Formats**: PDF, images (PNG, JPG, HEIC), text files
- **Smart Categories**: IDs, Bills, Certificates, Personal Notes, Other
- **Search & Filter**: Real-time search by name and category filtering

### 🎨 User Experience
- **Modern SwiftUI Interface**: Clean, intuitive design following iOS Human Interface Guidelines
- **Dark Mode Support**: Automatic light/dark mode adaptation
- **Responsive Layout**: Optimized for all iPhone screen sizes
- **Grid/List Views**: Toggle between card grid and list layouts
- **Quick Actions**: Context menus and swipe actions

### 📱 Document Viewing
- **QuickLook Integration**: Native iOS document preview
- **PDF Support**: Dedicated PDF viewer with PDFKit
- **Thumbnail Generation**: Automatic thumbnails for images and PDFs
- **Document Details**: File size, creation date, category information

## Technical Requirements

- **iOS 15.0+**: Targets modern iOS versions for latest SwiftUI features
- **Xcode 15.0+**: Latest development tools
- **Swift 5.0+**: Modern Swift language features

## Project Structure

```
MyDocs Vault/
├── App/
│   └── MyDocsVaultApp.swift          # Main app entry point
├── Views/
│   ├── ContentView.swift             # Root view with authentication flow
│   ├── DocumentListView.swift        # Home screen with document grid/list
│   ├── DocumentCardView.swift        # Document card and row components
│   ├── DocumentImportView.swift      # Import from various sources
│   ├── DocumentDetailView.swift      # Document details and actions
│   ├── QuickLookView.swift          # Document preview integration
│   └── CategoryManagementView.swift  # Category management interface
├── Models/
│   └── Document.swift               # Document and Category data models
├── Utilities/
│   ├── AuthenticationManager.swift  # Biometric authentication handling
│   └── DocumentManager.swift        # File operations and document storage
├── Assets.xcassets/                 # App icons and assets
└── Info.plist                      # App configuration and permissions
```

## Permissions Required

The app requests the following permissions (configured in Info.plist):

- **Face ID/Touch ID**: `NSFaceIDUsageDescription`
- **Camera Access**: `NSCameraUsageDescription` 
- **Photo Library**: `NSPhotoLibraryUsageDescription`

## Installation & Setup

1. **Open in Xcode**: Open `MyDocs Vault.xcodeproj` in Xcode 15.0+
2. **Configure Team**: Set your development team in project settings
3. **Update Bundle ID**: Change the bundle identifier to your own
4. **Build & Run**: Select a target device/simulator and build

## Key Components

### Authentication System
- Automatic biometric authentication on app launch
- Fallback to device passcode when biometrics unavailable
- Comprehensive error handling for all authentication scenarios

### File Management
- Secure sandbox storage with organized directory structure
- Automatic thumbnail generation for supported file types
- JSON-based metadata storage for document information
- File integrity verification on app launch

### Document Categories
- Predefined categories with custom icons and colors
- Easy category assignment and filtering
- Document count tracking per category

## Security Features

- **Local-Only Storage**: Documents never leave the device
- **Biometric Protection**: App locks automatically when backgrounded
- **Secure File Handling**: All files stored in app's private sandbox
- **No Network Access**: App works completely offline

## Future Enhancements

- iCloud Drive integration (optional)
- Document sharing capabilities
- Basic text editing for notes
- Export functionality
- Document encryption
- Backup/restore features

## Development Notes

- Uses `@StateObject` and `@ObservableObject` for reactive data management
- Implements proper error handling for file operations
- Follows iOS memory management best practices
- Supports both light and dark appearance modes
- Responsive design for various screen sizes

## License

This project is provided as-is for educational and development purposes.
