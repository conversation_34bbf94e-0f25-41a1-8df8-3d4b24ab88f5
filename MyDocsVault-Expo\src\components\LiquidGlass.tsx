import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface LiquidGlassProps {
  children: React.ReactNode;
  style?: ViewStyle;
  intensity?: 'light' | 'medium' | 'strong';
  variant?: 'primary' | 'secondary' | 'accent';
}

export const LiquidGlass: React.FC<LiquidGlassProps> = ({
  children,
  style,
  intensity = 'medium',
  variant = 'primary'
}) => {
  const getGlassStyles = () => {
    const baseStyles = {
      borderRadius: 24,
      overflow: 'hidden' as const,
      position: 'relative' as const,
    };

    const intensityStyles = {
      light: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.2)',
      },
      medium: {
        backgroundColor: 'rgba(255, 255, 255, 0.15)',
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
      },
      strong: {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1.5,
        borderColor: 'rgba(255, 255, 255, 0.4)',
      }
    };

    const variantStyles = {
      primary: {
        shadowColor: 'rgba(31, 38, 135, 0.37)',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 1,
        shadowRadius: 32,
        elevation: 8,
      },
      secondary: {
        shadowColor: 'rgba(255, 255, 255, 0.25)',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 1,
        shadowRadius: 16,
        elevation: 4,
      },
      accent: {
        shadowColor: 'rgba(138, 43, 226, 0.3)',
        shadowOffset: { width: 0, height: 12 },
        shadowOpacity: 1,
        shadowRadius: 24,
        elevation: 12,
      }
    };

    return {
      ...baseStyles,
      ...intensityStyles[intensity],
      ...variantStyles[variant],
    };
  };

  const getGradientColors = () => {
    const gradients = {
      primary: ['rgba(255, 255, 255, 0.25)', 'rgba(255, 255, 255, 0.05)'],
      secondary: ['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)'],
      accent: ['rgba(138, 43, 226, 0.2)', 'rgba(75, 0, 130, 0.1)'],
    };
    return gradients[variant];
  };

  return (
    <View style={[getGlassStyles(), style]}>
      {/* Main glass container */}
      <View style={styles.glassContainer}>
        {children}
      </View>
      
      {/* Liquid shine effect overlay */}
      <View style={styles.shineOverlay}>
        <LinearGradient
          colors={getGradientColors()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.shineGradient}
        />
        
        {/* Inner glow effect */}
        <View style={[styles.innerGlow, {
          shadowColor: variant === 'accent' ? 'rgba(138, 43, 226, 0.6)' : 'rgba(255, 255, 255, 0.6)',
        }]} />
        
        {/* Liquid highlight */}
        <View style={styles.liquidHighlight} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  glassContainer: {
    position: 'relative',
    zIndex: 2,
  },
  shineOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    borderRadius: 24,
  },
  shineGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.6,
  },
  innerGlow: {
    position: 'absolute',
    top: 2,
    left: 2,
    right: 2,
    height: '30%',
    borderRadius: 22,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  liquidHighlight: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '40%',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    opacity: 0.8,
  },
});
