//
//  Document.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import Foundation
import SwiftUI

struct Document: Identifiable, Codable, Hashable {
    let id = UUID()
    var name: String
    var fileName: String
    var fileExtension: String
    var category: DocumentCategory
    var dateCreated: Date
    var dateModified: Date
    var fileSize: Int64
    var thumbnailPath: String?
    var filePath: String
    
    init(name: String, fileName: String, fileExtension: String, category: DocumentCategory, filePath: String, fileSize: Int64) {
        self.name = name
        self.fileName = fileName
        self.fileExtension = fileExtension
        self.category = category
        self.dateCreated = Date()
        self.dateModified = Date()
        self.fileSize = fileSize
        self.filePath = filePath
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    var isImage: Bool {
        let imageExtensions = ["jpg", "jpeg", "png", "heic", "gif", "bmp", "tiff"]
        return imageExtensions.contains(fileExtension.lowercased())
    }
    
    var isPDF: Bool {
        return fileExtension.lowercased() == "pdf"
    }
    
    var isText: Bool {
        let textExtensions = ["txt", "md", "rtf"]
        return textExtensions.contains(fileExtension.lowercased())
    }
    
    var systemImageName: String {
        switch fileExtension.lowercased() {
        case "pdf":
            return "doc.fill"
        case "jpg", "jpeg", "png", "heic", "gif", "bmp", "tiff":
            return "photo.fill"
        case "txt", "md", "rtf":
            return "doc.text.fill"
        default:
            return "doc.fill"
        }
    }
    
    mutating func updateModifiedDate() {
        self.dateModified = Date()
    }
}

enum DocumentCategory: String, CaseIterable, Codable {
    case ids = "IDs"
    case bills = "Bills"
    case certificates = "Certificates"
    case personalNotes = "Personal Notes"
    case other = "Other"
    
    var displayName: String {
        return self.rawValue
    }
    
    var systemImageName: String {
        switch self {
        case .ids:
            return "person.crop.rectangle"
        case .bills:
            return "receipt"
        case .certificates:
            return "rosette"
        case .personalNotes:
            return "note.text"
        case .other:
            return "folder"
        }
    }
    
    var color: Color {
        switch self {
        case .ids:
            return .blue
        case .bills:
            return .green
        case .certificates:
            return .purple
        case .personalNotes:
            return .orange
        case .other:
            return .gray
        }
    }
}
