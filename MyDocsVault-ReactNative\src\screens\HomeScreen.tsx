import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  useColorScheme,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  FAB,
  Searchbar,
  SegmentedButtons,
  Card,
  Title,
  Paragraph,
  IconButton,
} from 'react-native-paper';
import {useFocusEffect} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';

import {Document, DocumentCategory, CATEGORY_INFO} from '../types/Document';
import {DocumentService} from '../services/DocumentService';
import {RootStackParamList} from '../../App';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<string>('grid');
  const [isLoading, setIsLoading] = useState(false);
  const [categoryCounts, setCategoryCounts] = useState<Record<DocumentCategory, number>>({
    [DocumentCategory.IDS]: 0,
    [DocumentCategory.BILLS]: 0,
    [DocumentCategory.CERTIFICATES]: 0,
    [DocumentCategory.PERSONAL_NOTES]: 0,
    [DocumentCategory.OTHER]: 0,
  });

  useFocusEffect(
    useCallback(() => {
      loadDocuments();
    }, [])
  );

  const loadDocuments = async () => {
    setIsLoading(true);
    try {
      const allDocs = await DocumentService.getAllDocuments();
      const counts = await DocumentService.getCategoryCounts();
      
      setDocuments(allDocs);
      setCategoryCounts(counts);
      filterDocuments(allDocs, searchQuery, selectedCategory);
    } catch (error) {
      console.error('Error loading documents:', error);
      Alert.alert('Error', 'Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const filterDocuments = (
    docs: Document[],
    query: string,
    category: string,
  ) => {
    let filtered = docs;

    // Filter by category
    if (category !== 'all') {
      filtered = filtered.filter(doc => doc.category === category);
    }

    // Filter by search query
    if (query.trim()) {
      const lowercaseQuery = query.toLowerCase();
      filtered = filtered.filter(
        doc =>
          doc.name.toLowerCase().includes(lowercaseQuery) ||
          doc.description?.toLowerCase().includes(lowercaseQuery),
      );
    }

    setFilteredDocuments(filtered);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterDocuments(documents, query, selectedCategory);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    filterDocuments(documents, searchQuery, category);
  };

  const handleDocumentPress = (document: Document) => {
    navigation.navigate('DocumentDetail', {documentId: document.id});
  };

  const handleDeleteDocument = async (documentId: string) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document? This action cannot be undone.',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await DocumentService.deleteDocument(documentId);
            if (success) {
              loadDocuments();
            } else {
              Alert.alert('Error', 'Failed to delete document');
            }
          },
        },
      ],
    );
  };

  const renderDocumentCard = ({item}: {item: Document}) => {
    const categoryInfo = CATEGORY_INFO[item.category];
    
    return (
      <Card
        style={[styles.documentCard, viewMode === 'list' && styles.listCard]}
        onPress={() => handleDocumentPress(item)}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.cardInfo}>
              <Title style={styles.documentTitle} numberOfLines={1}>
                {item.name}
              </Title>
              <Paragraph style={styles.documentCategory}>
                {categoryInfo.category}
              </Paragraph>
              <Paragraph style={styles.documentSize}>
                {DocumentService.formatFileSize(item.fileSize)}
              </Paragraph>
            </View>
            <IconButton
              icon="delete"
              size={20}
              onPress={() => handleDeleteDocument(item.id)}
            />
          </View>
        </Card.Content>
      </Card>
    );
  };

  const categoryButtons = [
    {value: 'all', label: `All (${documents.length})`},
    ...Object.values(DocumentCategory).map(category => ({
      value: category,
      label: `${category} (${categoryCounts[category]})`,
    })),
  ];

  const viewModeButtons = [
    {value: 'grid', label: 'Grid'},
    {value: 'list', label: 'List'},
  ];

  const styles = getStyles(isDarkMode);

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <Searchbar
        placeholder="Search documents..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
      />

      {/* Category Filter */}
      <SegmentedButtons
        value={selectedCategory}
        onValueChange={handleCategoryChange}
        buttons={categoryButtons}
        style={styles.categoryFilter}
      />

      {/* View Mode Toggle */}
      <SegmentedButtons
        value={viewMode}
        onValueChange={setViewMode}
        buttons={viewModeButtons}
        style={styles.viewModeToggle}
      />

      {/* Documents List */}
      {filteredDocuments.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📄</Text>
          <Text style={styles.emptyStateTitle}>No Documents Found</Text>
          <Text style={styles.emptyStateDescription}>
            {searchQuery || selectedCategory !== 'all'
              ? 'Try adjusting your search or filter'
              : 'Tap the + button to add your first document'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredDocuments}
          renderItem={renderDocumentCard}
          keyExtractor={item => item.id}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
          contentContainerStyle={styles.documentsList}
          refreshControl={
            <RefreshControl refreshing={isLoading} onRefresh={loadDocuments} />
          }
        />
      )}

      {/* Add Document FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('DocumentImport')}
      />
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    searchBar: {
      margin: 16,
      marginBottom: 8,
    },
    categoryFilter: {
      marginHorizontal: 16,
      marginBottom: 8,
    },
    viewModeToggle: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    documentsList: {
      padding: 8,
    },
    documentCard: {
      margin: 8,
      flex: 1,
    },
    listCard: {
      marginHorizontal: 16,
      marginVertical: 4,
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    cardInfo: {
      flex: 1,
    },
    documentTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    documentCategory: {
      fontSize: 14,
      color: isDarkMode ? '#cccccc' : '#666666',
      marginBottom: 2,
    },
    documentSize: {
      fontSize: 12,
      color: isDarkMode ? '#aaaaaa' : '#888888',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyStateIcon: {
      fontSize: 64,
      marginBottom: 16,
    },
    emptyStateTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyStateDescription: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
      lineHeight: 22,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: 0,
      backgroundColor: '#007AFF',
    },
  });

export default HomeScreen;
