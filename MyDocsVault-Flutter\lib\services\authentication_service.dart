import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import '../models/document.dart';

class AuthenticationService extends ChangeNotifier {
  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _isAuthenticated = false;
  String? _biometryType;

  bool get isAuthenticated => _isAuthenticated;
  String? get biometryType => _biometryType;

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Get biometry type as string for display
  Future<String?> getBiometryTypeString() async {
    try {
      final biometrics = await getAvailableBiometrics();
      if (biometrics.isEmpty) return null;

      if (biometrics.contains(BiometricType.face)) {
        return 'Face ID';
      } else if (biometrics.contains(BiometricType.fingerprint)) {
        return 'Touch ID';
      } else if (biometrics.contains(BiometricType.iris)) {
        return 'Iris';
      } else {
        return 'Biometric';
      }
    } catch (e) {
      debugPrint('Error getting biometry type: $e');
      return null;
    }
  }

  /// Authenticate user using biometrics or device credentials
  Future<AuthenticationResult> authenticate() async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      
      if (!isAvailable) {
        return AuthenticationResult(
          success: false,
          error: 'Biometric authentication not available',
        );
      }

      final biometryType = await getBiometryTypeString();
      final String promptMessage = _getPromptMessage(biometryType);

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: promptMessage,
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        _isAuthenticated = true;
        _biometryType = biometryType;
        notifyListeners();
        
        return AuthenticationResult(
          success: true,
          biometryType: biometryType,
        );
      } else {
        return AuthenticationResult(
          success: false,
          error: 'Authentication was cancelled or failed',
        );
      }
    } catch (e) {
      debugPrint('Authentication error: $e');
      return AuthenticationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Get appropriate prompt message based on biometry type
  String _getPromptMessage(String? biometryType) {
    switch (biometryType) {
      case 'Touch ID':
        return 'Use Touch ID to access your secure documents';
      case 'Face ID':
        return 'Use Face ID to access your secure documents';
      case 'Iris':
        return 'Use Iris authentication to access your secure documents';
      default:
        return 'Use biometric authentication to access your secure documents';
    }
  }

  /// Get biometry icon for display
  String getBiometryIcon(String? biometryType) {
    switch (biometryType) {
      case 'Touch ID':
        return '👆';
      case 'Face ID':
        return '👤';
      case 'Iris':
        return '👁️';
      default:
        return '🔒';
    }
  }

  /// Logout user
  void logout() {
    _isAuthenticated = false;
    _biometryType = null;
    notifyListeners();
  }

  /// Check if device has PIN, pattern, or password set up
  Future<bool> isDeviceSecure() async {
    try {
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isDeviceSupported;
    } catch (e) {
      debugPrint('Error checking device security: $e');
      return false;
    }
  }

  /// Stop authentication (if in progress)
  Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
    } catch (e) {
      debugPrint('Error stopping authentication: $e');
    }
  }
}
