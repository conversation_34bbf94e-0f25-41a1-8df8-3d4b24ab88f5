import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Document,
  DocumentCategory,
  DocumentMetadata,
  ImportResult,
  SUPPORTED_FILE_TYPES,
  IMAGE_EXTENSIONS,
} from '../types/Document';

export class DocumentService {
  private static readonly DOCUMENTS_DIR = `${RNFS.DocumentDirectoryPath}/MyDocsVault`;
  private static readonly THUMBNAILS_DIR = `${DocumentService.DOCUMENTS_DIR}/Thumbnails`;
  private static readonly METADATA_KEY = 'documents_metadata';

  /**
   * Initialize the document service and create necessary directories
   */
  static async initialize(): Promise<void> {
    try {
      // Create documents directory
      const documentsExists = await RNFS.exists(this.DOCUMENTS_DIR);
      if (!documentsExists) {
        await RNFS.mkdir(this.DOCUMENTS_DIR);
      }

      // Create thumbnails directory
      const thumbnailsExists = await RNFS.exists(this.THUMBNAILS_DIR);
      if (!thumbnailsExists) {
        await RNFS.mkdir(this.THUMBNAILS_DIR);
      }

      console.log('DocumentService initialized successfully');
    } catch (error) {
      console.error('Error initializing DocumentService:', error);
      throw error;
    }
  }

  /**
   * Get all documents
   */
  static async getAllDocuments(): Promise<Document[]> {
    try {
      const metadataJson = await AsyncStorage.getItem(this.METADATA_KEY);
      if (!metadataJson) {
        return [];
      }

      const metadata: DocumentMetadata[] = JSON.parse(metadataJson);
      return metadata.map(this.metadataToDocument);
    } catch (error) {
      console.error('Error getting documents:', error);
      return [];
    }
  }

  /**
   * Get documents by category
   */
  static async getDocumentsByCategory(category: DocumentCategory): Promise<Document[]> {
    const allDocuments = await this.getAllDocuments();
    return allDocuments.filter(doc => doc.category === category);
  }

  /**
   * Search documents by name
   */
  static async searchDocuments(query: string): Promise<Document[]> {
    const allDocuments = await this.getAllDocuments();
    const lowercaseQuery = query.toLowerCase();
    
    return allDocuments.filter(doc =>
      doc.name.toLowerCase().includes(lowercaseQuery) ||
      doc.description?.toLowerCase().includes(lowercaseQuery)
    );
  }

  /**
   * Get document by ID
   */
  static async getDocumentById(id: string): Promise<Document | null> {
    const allDocuments = await this.getAllDocuments();
    return allDocuments.find(doc => doc.id === id) || null;
  }

  /**
   * Import a document from file URI
   */
  static async importDocument(
    fileUri: string,
    name: string,
    category: DocumentCategory,
    description?: string,
  ): Promise<ImportResult> {
    try {
      // Get file info
      const fileInfo = await RNFS.stat(fileUri);
      const fileName = fileInfo.name || `document_${Date.now()}`;
      const fileExtension = this.getFileExtension(fileName);

      // Validate file type
      if (!SUPPORTED_FILE_TYPES.includes(fileExtension.toLowerCase())) {
        return {
          success: false,
          error: `Unsupported file type: ${fileExtension}`,
        };
      }

      // Generate unique ID and file path
      const documentId = this.generateId();
      const newFileName = `${documentId}.${fileExtension}`;
      const destinationPath = `${this.DOCUMENTS_DIR}/${newFileName}`;

      // Copy file to documents directory
      await RNFS.copyFile(fileUri, destinationPath);

      // Generate thumbnail if it's an image
      let thumbnailPath: string | undefined;
      if (IMAGE_EXTENSIONS.includes(fileExtension.toLowerCase())) {
        thumbnailPath = await this.generateThumbnail(destinationPath, documentId);
      }

      // Create document object
      const document: Document = {
        id: documentId,
        name: name || fileName,
        fileName: newFileName,
        fileExtension,
        category,
        dateCreated: new Date(),
        dateModified: new Date(),
        fileSize: fileInfo.size,
        thumbnailPath,
        filePath: destinationPath,
        description,
      };

      // Save metadata
      await this.saveDocumentMetadata(document);

      return {
        success: true,
        document,
      };
    } catch (error) {
      console.error('Error importing document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Import failed',
      };
    }
  }

  /**
   * Delete a document
   */
  static async deleteDocument(documentId: string): Promise<boolean> {
    try {
      const document = await this.getDocumentById(documentId);
      if (!document) {
        return false;
      }

      // Delete file
      const fileExists = await RNFS.exists(document.filePath);
      if (fileExists) {
        await RNFS.unlink(document.filePath);
      }

      // Delete thumbnail
      if (document.thumbnailPath) {
        const thumbnailExists = await RNFS.exists(document.thumbnailPath);
        if (thumbnailExists) {
          await RNFS.unlink(document.thumbnailPath);
        }
      }

      // Remove from metadata
      await this.removeDocumentMetadata(documentId);

      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      return false;
    }
  }

  /**
   * Update document metadata
   */
  static async updateDocument(document: Document): Promise<boolean> {
    try {
      await this.saveDocumentMetadata(document);
      return true;
    } catch (error) {
      console.error('Error updating document:', error);
      return false;
    }
  }

  /**
   * Get document categories with counts
   */
  static async getCategoryCounts(): Promise<Record<DocumentCategory, number>> {
    const allDocuments = await this.getAllDocuments();
    const counts: Record<DocumentCategory, number> = {
      [DocumentCategory.IDS]: 0,
      [DocumentCategory.BILLS]: 0,
      [DocumentCategory.CERTIFICATES]: 0,
      [DocumentCategory.PERSONAL_NOTES]: 0,
      [DocumentCategory.OTHER]: 0,
    };

    allDocuments.forEach(doc => {
      counts[doc.category]++;
    });

    return counts;
  }

  /**
   * Generate thumbnail for image files
   */
  private static async generateThumbnail(
    filePath: string,
    documentId: string,
  ): Promise<string> {
    try {
      const thumbnailPath = `${this.THUMBNAILS_DIR}/${documentId}_thumb.jpg`;
      
      // For now, we'll just copy the original image as thumbnail
      // In a real implementation, you'd resize the image
      await RNFS.copyFile(filePath, thumbnailPath);
      
      return thumbnailPath;
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      throw error;
    }
  }

  /**
   * Save document metadata to AsyncStorage
   */
  private static async saveDocumentMetadata(document: Document): Promise<void> {
    try {
      const allDocuments = await this.getAllDocuments();
      const existingIndex = allDocuments.findIndex(doc => doc.id === document.id);
      
      if (existingIndex >= 0) {
        allDocuments[existingIndex] = document;
      } else {
        allDocuments.push(document);
      }

      const metadata = allDocuments.map(this.documentToMetadata);
      await AsyncStorage.setItem(this.METADATA_KEY, JSON.stringify(metadata));
    } catch (error) {
      console.error('Error saving document metadata:', error);
      throw error;
    }
  }

  /**
   * Remove document metadata from AsyncStorage
   */
  private static async removeDocumentMetadata(documentId: string): Promise<void> {
    try {
      const allDocuments = await this.getAllDocuments();
      const filteredDocuments = allDocuments.filter(doc => doc.id !== documentId);
      
      const metadata = filteredDocuments.map(this.documentToMetadata);
      await AsyncStorage.setItem(this.METADATA_KEY, JSON.stringify(metadata));
    } catch (error) {
      console.error('Error removing document metadata:', error);
      throw error;
    }
  }

  /**
   * Convert Document to DocumentMetadata for storage
   */
  private static documentToMetadata(document: Document): DocumentMetadata {
    return {
      ...document,
      dateCreated: document.dateCreated.toISOString(),
      dateModified: document.dateModified.toISOString(),
    };
  }

  /**
   * Convert DocumentMetadata to Document
   */
  private static metadataToDocument(metadata: DocumentMetadata): Document {
    return {
      ...metadata,
      dateCreated: new Date(metadata.dateCreated),
      dateModified: new Date(metadata.dateModified),
    };
  }

  /**
   * Generate unique ID
   */
  private static generateId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get file extension from filename
   */
  private static getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot >= 0 ? fileName.substring(lastDot + 1) : '';
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
