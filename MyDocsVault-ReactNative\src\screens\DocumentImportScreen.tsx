import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  useColorScheme,
  ScrollView,
} from 'react-native';
import {
  Button,
  TextInput,
  SegmentedButtons,
  Card,
  Title,
  ActivityIndicator,
} from 'react-native-paper';
import DocumentPicker from 'react-native-document-picker';
import {launchImageLibrary, launchCamera, ImagePickerResponse} from 'react-native-image-picker';
import {StackNavigationProp} from '@react-navigation/stack';

import {DocumentCategory, CATEGORY_INFO} from '../types/Document';
import {DocumentService} from '../services/DocumentService';
import {RootStackParamList} from '../../App';

type DocumentImportNavigationProp = StackNavigationProp<
  RootStackParamList,
  'DocumentImport'
>;

interface Props {
  navigation: DocumentImportNavigationProp;
}

const DocumentImportScreen: React.FC<Props> = ({navigation}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [documentName, setDocumentName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<DocumentCategory>(
    DocumentCategory.OTHER,
  );
  const [isImporting, setIsImporting] = useState(false);

  const handleImportFromFiles = async () => {
    try {
      const result = await DocumentPicker.pickSingle({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.images,
          DocumentPicker.types.plainText,
        ],
      });

      if (result.uri) {
        await importDocument(result.uri, result.name || 'Imported Document');
      }
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        console.error('Document picker error:', error);
        Alert.alert('Error', 'Failed to pick document');
      }
    }
  };

  const handleImportFromCamera = () => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      includeBase64: false,
    };

    launchCamera(options, (response: ImagePickerResponse) => {
      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        if (asset.uri) {
          importDocument(asset.uri, asset.fileName || 'Camera Photo');
        }
      } else if (response.errorMessage) {
        Alert.alert('Error', response.errorMessage);
      }
    });
  };

  const handleImportFromGallery = () => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      includeBase64: false,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.assets && response.assets[0]) {
        const asset = response.assets[0];
        if (asset.uri) {
          importDocument(asset.uri, asset.fileName || 'Gallery Photo');
        }
      } else if (response.errorMessage) {
        Alert.alert('Error', response.errorMessage);
      }
    });
  };

  const handleCreateTextNote = async () => {
    if (!documentName.trim()) {
      Alert.alert('Error', 'Please enter a document name');
      return;
    }

    try {
      setIsImporting(true);
      
      // Create a temporary text file
      const textContent = description || 'New text note';
      const fileName = `${documentName.replace(/[^a-zA-Z0-9]/g, '_')}.txt`;
      
      // For now, we'll create a simple text file
      // In a real implementation, you'd write to a temporary file
      const result = await DocumentService.importDocument(
        '', // We'd need to create a temp file here
        documentName,
        selectedCategory,
        description,
      );

      if (result.success) {
        Alert.alert('Success', 'Text note created successfully', [
          {text: 'OK', onPress: () => navigation.goBack()},
        ]);
      } else {
        Alert.alert('Error', result.error || 'Failed to create text note');
      }
    } catch (error) {
      console.error('Error creating text note:', error);
      Alert.alert('Error', 'Failed to create text note');
    } finally {
      setIsImporting(false);
    }
  };

  const importDocument = async (fileUri: string, fileName: string) => {
    if (!documentName.trim()) {
      setDocumentName(fileName);
    }

    setIsImporting(true);
    
    try {
      const result = await DocumentService.importDocument(
        fileUri,
        documentName || fileName,
        selectedCategory,
        description,
      );

      if (result.success) {
        Alert.alert('Success', 'Document imported successfully', [
          {text: 'OK', onPress: () => navigation.goBack()},
        ]);
      } else {
        Alert.alert('Error', result.error || 'Failed to import document');
      }
    } catch (error) {
      console.error('Error importing document:', error);
      Alert.alert('Error', 'Failed to import document');
    } finally {
      setIsImporting(false);
    }
  };

  const categoryButtons = Object.values(DocumentCategory).map(category => ({
    value: category,
    label: category,
  }));

  const styles = getStyles(isDarkMode);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Document Information */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Document Information</Title>
          
          <TextInput
            label="Document Name"
            value={documentName}
            onChangeText={setDocumentName}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Description (Optional)"
            value={description}
            onChangeText={setDescription}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
          />
          
          <Text style={styles.sectionTitle}>Category</Text>
          <SegmentedButtons
            value={selectedCategory}
            onValueChange={setSelectedCategory}
            buttons={categoryButtons}
            style={styles.categoryButtons}
          />
        </Card.Content>
      </Card>

      {/* Import Options */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Import Source</Title>
          
          <Button
            mode="contained"
            icon="folder"
            onPress={handleImportFromFiles}
            style={styles.importButton}
            disabled={isImporting}>
            Import from Files
          </Button>
          
          <Button
            mode="contained"
            icon="camera"
            onPress={handleImportFromCamera}
            style={styles.importButton}
            disabled={isImporting}>
            Take Photo
          </Button>
          
          <Button
            mode="contained"
            icon="image"
            onPress={handleImportFromGallery}
            style={styles.importButton}
            disabled={isImporting}>
            Choose from Gallery
          </Button>
          
          <Button
            mode="contained"
            icon="note-text"
            onPress={handleCreateTextNote}
            style={styles.importButton}
            disabled={isImporting || !documentName.trim()}>
            Create Text Note
          </Button>
        </Card.Content>
      </Card>

      {/* Loading Indicator */}
      {isImporting && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Importing document...</Text>
        </View>
      )}

      {/* Supported Formats */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Supported Formats</Title>
          <Text style={styles.supportedFormats}>
            📄 PDF Documents{'\n'}
            🖼️ Images (JPG, PNG, HEIC){'\n'}
            📝 Text Files (TXT, MD, RTF)
          </Text>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    content: {
      padding: 16,
    },
    card: {
      marginBottom: 16,
    },
    input: {
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
      color: isDarkMode ? '#ffffff' : '#000000',
    },
    categoryButtons: {
      marginBottom: 16,
    },
    importButton: {
      marginBottom: 12,
      backgroundColor: '#007AFF',
    },
    loadingContainer: {
      alignItems: 'center',
      padding: 20,
    },
    loadingText: {
      marginTop: 8,
      fontSize: 16,
      color: isDarkMode ? '#ffffff' : '#000000',
    },
    supportedFormats: {
      fontSize: 14,
      color: isDarkMode ? '#cccccc' : '#666666',
      lineHeight: 20,
    },
  });

export default DocumentImportScreen;
