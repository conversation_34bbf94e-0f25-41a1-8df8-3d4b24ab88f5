//
//  DocumentCardView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI

struct DocumentCardView: View {
    let document: Document
    @EnvironmentObject var documentManager: DocumentManager
    @State private var showingDeleteAlert = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Thumbnail or Icon
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .frame(height: 120)
                
                if let thumbnailImage = documentManager.getThumbnailImage(for: document) {
                    Image(uiImage: thumbnailImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 120)
                        .clipped()
                        .cornerRadius(12)
                } else {
                    Image(systemName: document.systemImageName)
                        .font(.system(size: 40))
                        .foregroundColor(document.category.color)
                }
            }
            
            // Document Info
            VStack(alignment: .leading, spacing: 4) {
                Text(document.name)
                    .font(.headline)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                HStack {
                    Image(systemName: document.category.systemImageName)
                        .foregroundColor(document.category.color)
                        .font(.caption)
                    
                    Text(document.category.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                
                Text(document.formattedFileSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(document.dateModified, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .contextMenu {
            Button(action: {
                // Share functionality would go here
            }) {
                Label("Share", systemImage: "square.and.arrow.up")
            }
            
            Button(role: .destructive, action: {
                showingDeleteAlert = true
            }) {
                Label("Delete", systemImage: "trash")
            }
        }
        .alert("Delete Document", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                documentManager.deleteDocument(document)
            }
        } message: {
            Text("Are you sure you want to delete '\(document.name)'? This action cannot be undone.")
        }
    }
}

struct DocumentRowView: View {
    let document: Document
    @EnvironmentObject var documentManager: DocumentManager
    @State private var showingDeleteAlert = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Thumbnail or Icon
            ZStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
                    .frame(width: 60, height: 60)
                
                if let thumbnailImage = documentManager.getThumbnailImage(for: document) {
                    Image(uiImage: thumbnailImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 60, height: 60)
                        .clipped()
                        .cornerRadius(8)
                } else {
                    Image(systemName: document.systemImageName)
                        .font(.system(size: 24))
                        .foregroundColor(document.category.color)
                }
            }
            
            // Document Info
            VStack(alignment: .leading, spacing: 4) {
                Text(document.name)
                    .font(.headline)
                    .lineLimit(1)
                
                HStack {
                    Image(systemName: document.category.systemImageName)
                        .foregroundColor(document.category.color)
                        .font(.caption)
                    
                    Text(document.category.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text(document.formattedFileSize)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text(document.dateModified, style: .date)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // More button
            Button(action: {
                showingDeleteAlert = true
            }) {
                Image(systemName: "ellipsis")
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        .alert("Delete Document", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                documentManager.deleteDocument(document)
            }
        } message: {
            Text("Are you sure you want to delete '\(document.name)'? This action cannot be undone.")
        }
    }
}

#Preview {
    VStack {
        DocumentCardView(document: Document(
            name: "Sample Document",
            fileName: "sample.pdf",
            fileExtension: "pdf",
            category: .certificates,
            filePath: "/path/to/file",
            fileSize: 1024000
        ))
        .frame(width: 150)
        
        DocumentRowView(document: Document(
            name: "Sample Document",
            fileName: "sample.pdf",
            fileExtension: "pdf",
            category: .certificates,
            filePath: "/path/to/file",
            fileSize: 1024000
        ))
    }
    .environmentObject(DocumentManager())
    .padding()
}
