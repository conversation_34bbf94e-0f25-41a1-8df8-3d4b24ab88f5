import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import 'services/authentication_service.dart';
import 'services/document_service.dart';
import 'screens/authentication_screen.dart';
import 'screens/home_screen.dart';
import 'screens/document_detail_screen.dart';
import 'screens/document_import_screen.dart';
import 'screens/category_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  await DocumentService.initialize();
  
  runApp(MyDocsVaultApp());
}

class MyDocsVaultApp extends StatelessWidget {
  MyDocsVaultApp({Key? key}) : super(key: key);

  final GoRouter _router = GoRouter(
    initialLocation: '/auth',
    routes: [
      GoRoute(
        path: '/auth',
        builder: (context, state) => const AuthenticationScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/document/:id',
        builder: (context, state) => DocumentDetailScreen(
          documentId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/import',
        builder: (context, state) => const DocumentImportScreen(),
      ),
      GoRoute(
        path: '/category/:category',
        builder: (context, state) => CategoryScreen(
          category: state.pathParameters['category']!,
        ),
      ),
    ],
  );

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthenticationService()),
        ChangeNotifierProvider(create: (_) => DocumentService()),
      ],
      child: MaterialApp.router(
        title: 'MyDocs Vault',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF007AFF),
          visualDensity: VisualDensity.adaptivePlatformDensity,
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF007AFF),
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          floatingActionButtonTheme: const FloatingActionButtonThemeData(
            backgroundColor: Color(0xFF007AFF),
          ),
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
        ),
        darkTheme: ThemeData.dark().copyWith(
          primaryColor: const Color(0xFF007AFF),
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF1a1a1a),
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          floatingActionButtonTheme: const FloatingActionButtonThemeData(
            backgroundColor: Color(0xFF007AFF),
          ),
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: const Color(0xFF2a2a2a),
          ),
          scaffoldBackgroundColor: Colors.black,
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF333333),
          ),
        ),
        themeMode: ThemeMode.system,
        routerConfig: _router,
      ),
    );
  }
}
