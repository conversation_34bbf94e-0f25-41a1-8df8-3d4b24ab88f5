import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, Alert } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import * as LocalAuthentication from 'expo-local-authentication';

// Import screens
import AuthenticationScreen from './src/screens/AuthenticationScreen';
import HomeScreen from './src/screens/HomeScreen';
import DocumentDetailScreen from './src/screens/DocumentDetailScreen';
import DocumentImportScreen from './src/screens/DocumentImportScreen';

export type RootStackParamList = {
  Authentication: undefined;
  Home: undefined;
  DocumentDetail: { documentId: string };
  DocumentImport: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkBiometricSupport();
  }, []);

  const checkBiometricSupport = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware || !isEnrolled) {
        Alert.alert(
          'Biometric Authentication',
          'Biometric authentication is not available on this device. The app will use device PIN/Password.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error checking biometric support:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthenticationSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  if (isLoading) {
    return <View style={styles.container} />;
  }

  return (
    <PaperProvider>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName={isAuthenticated ? 'Home' : 'Authentication'}
          screenOptions={{
            headerStyle: {
              backgroundColor: '#007AFF',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}>
          {!isAuthenticated ? (
            <Stack.Screen
              name="Authentication"
              options={{ headerShown: false }}>
              {(props) => (
                <AuthenticationScreen
                  {...props}
                  onAuthenticationSuccess={handleAuthenticationSuccess}
                />
              )}
            </Stack.Screen>
          ) : (
            <>
              <Stack.Screen
                name="Home"
                options={{ headerShown: false }}>
                {(props) => (
                  <HomeScreen
                    {...props}
                    onLogout={handleLogout}
                  />
                )}
              </Stack.Screen>
              <Stack.Screen
                name="DocumentDetail"
                component={DocumentDetailScreen}
                options={{ title: 'Document Details' }}
              />
              <Stack.Screen
                name="DocumentImport"
                component={DocumentImportScreen}
                options={{ title: 'Import Document' }}
              />
            </>
          )}
        </Stack.Navigator>
        <StatusBar style="light" />
      </NavigationContainer>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
