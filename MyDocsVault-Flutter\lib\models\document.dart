import 'dart:convert';

enum DocumentCategory {
  ids('IDs'),
  bills('Bills'),
  certificates('Certificates'),
  personalNotes('Personal Notes'),
  other('Other');

  const DocumentCategory(this.displayName);
  final String displayName;
}

class CategoryInfo {
  final String category;
  final String icon;
  final String color;

  const CategoryInfo({
    required this.category,
    required this.icon,
    required this.color,
  });
}

const Map<DocumentCategory, CategoryInfo> categoryInfo = {
  DocumentCategory.ids: CategoryInfo(
    category: 'IDs',
    icon: '🆔',
    color: '#FF6B6B',
  ),
  DocumentCategory.bills: CategoryInfo(
    category: 'Bills',
    icon: '🧾',
    color: '#4ECDC4',
  ),
  DocumentCategory.certificates: CategoryInfo(
    category: 'Certificates',
    icon: '🏆',
    color: '#45B7D1',
  ),
  DocumentCategory.personalNotes: CategoryInfo(
    category: 'Personal Notes',
    icon: '📝',
    color: '#96CEB4',
  ),
  DocumentCategory.other: CategoryInfo(
    category: 'Other',
    icon: '📁',
    color: '#FFEAA7',
  ),
};

class Document {
  final String id;
  final String name;
  final String fileName;
  final String fileExtension;
  final DocumentCategory category;
  final DateTime dateCreated;
  final DateTime dateModified;
  final int fileSize;
  final String? thumbnailPath;
  final String filePath;
  final String? description;

  Document({
    required this.id,
    required this.name,
    required this.fileName,
    required this.fileExtension,
    required this.category,
    required this.dateCreated,
    required this.dateModified,
    required this.fileSize,
    this.thumbnailPath,
    required this.filePath,
    this.description,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'fileName': fileName,
      'fileExtension': fileExtension,
      'category': category.name,
      'dateCreated': dateCreated.millisecondsSinceEpoch,
      'dateModified': dateModified.millisecondsSinceEpoch,
      'fileSize': fileSize,
      'thumbnailPath': thumbnailPath,
      'filePath': filePath,
      'description': description,
    };
  }

  factory Document.fromMap(Map<String, dynamic> map) {
    return Document(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      fileName: map['fileName'] ?? '',
      fileExtension: map['fileExtension'] ?? '',
      category: DocumentCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => DocumentCategory.other,
      ),
      dateCreated: DateTime.fromMillisecondsSinceEpoch(map['dateCreated'] ?? 0),
      dateModified: DateTime.fromMillisecondsSinceEpoch(map['dateModified'] ?? 0),
      fileSize: map['fileSize']?.toInt() ?? 0,
      thumbnailPath: map['thumbnailPath'],
      filePath: map['filePath'] ?? '',
      description: map['description'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Document.fromJson(String source) => Document.fromMap(json.decode(source));

  Document copyWith({
    String? id,
    String? name,
    String? fileName,
    String? fileExtension,
    DocumentCategory? category,
    DateTime? dateCreated,
    DateTime? dateModified,
    int? fileSize,
    String? thumbnailPath,
    String? filePath,
    String? description,
  }) {
    return Document(
      id: id ?? this.id,
      name: name ?? this.name,
      fileName: fileName ?? this.fileName,
      fileExtension: fileExtension ?? this.fileExtension,
      category: category ?? this.category,
      dateCreated: dateCreated ?? this.dateCreated,
      dateModified: dateModified ?? this.dateModified,
      fileSize: fileSize ?? this.fileSize,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      filePath: filePath ?? this.filePath,
      description: description ?? this.description,
    );
  }

  @override
  String toString() {
    return 'Document(id: $id, name: $name, fileName: $fileName, fileExtension: $fileExtension, category: $category, dateCreated: $dateCreated, dateModified: $dateModified, fileSize: $fileSize, thumbnailPath: $thumbnailPath, filePath: $filePath, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Document &&
        other.id == id &&
        other.name == name &&
        other.fileName == fileName &&
        other.fileExtension == fileExtension &&
        other.category == category &&
        other.dateCreated == dateCreated &&
        other.dateModified == dateModified &&
        other.fileSize == fileSize &&
        other.thumbnailPath == thumbnailPath &&
        other.filePath == filePath &&
        other.description == description;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        fileName.hashCode ^
        fileExtension.hashCode ^
        category.hashCode ^
        dateCreated.hashCode ^
        dateModified.hashCode ^
        fileSize.hashCode ^
        thumbnailPath.hashCode ^
        filePath.hashCode ^
        description.hashCode;
  }
}

class AuthenticationResult {
  final bool success;
  final String? error;
  final String? biometryType;

  AuthenticationResult({
    required this.success,
    this.error,
    this.biometryType,
  });
}

class ImportResult {
  final bool success;
  final Document? document;
  final String? error;

  ImportResult({
    required this.success,
    this.document,
    this.error,
  });
}

// Supported file types
const List<String> supportedFileTypes = [
  'pdf',
  'jpg',
  'jpeg',
  'png',
  'heic',
  'txt',
  'md',
  'rtf',
];

const List<String> imageExtensions = [
  'jpg',
  'jpeg',
  'png',
  'heic',
  'gif',
  'bmp',
  'webp',
];

// Utility functions
String formatFileSize(int bytes) {
  if (bytes <= 0) return '0 B';
  const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
  var i = (bytes.bitLength - 1) ~/ 10;
  if (i >= suffixes.length) i = suffixes.length - 1;
  return '${(bytes / (1 << (i * 10))).toStringAsFixed(1)} ${suffixes[i]}';
}
