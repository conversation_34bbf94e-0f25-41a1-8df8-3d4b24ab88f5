import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  useColorScheme,
  Dimensions,
  ImageBackground,
  StatusBar,
} from 'react-native';
import { Button, ActivityIndicator, IconButton } from 'react-native-paper';
import * as LocalAuthentication from 'expo-local-authentication';
import { LinearGradient } from 'expo-linear-gradient';
import { LiquidGlass } from '../components/LiquidGlass';

const { width, height } = Dimensions.get('window');

interface Props {
  onAuthenticationSuccess: () => void;
}

const AuthenticationScreen: React.FC<Props> = ({ onAuthenticationSuccess }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [isLoading, setIsLoading] = useState(false);
  const [biometryType, setBiometryType] = useState<string | null>(null);
  const [isBiometricSupported, setIsBiometricSupported] = useState(false);
  const [isEnrolled, setIsEnrolled] = useState(false);

  useEffect(() => {
    checkBiometrySupport();
  }, []);

  const checkBiometrySupport = async () => {
    try {
      // Check if device has biometric hardware
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      setIsBiometricSupported(hasHardware);

      // Check if biometrics are enrolled
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setIsEnrolled(enrolled);

      // Get supported authentication types
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

      if (hasHardware && supportedTypes.length > 0) {
        if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
          setBiometryType('Face ID');
        } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
          setBiometryType('Touch ID');
        } else {
          setBiometryType('Biometric');
        }
      } else {
        setBiometryType('Device PIN');
      }

      console.log('Biometric support:', { hasHardware, enrolled, supportedTypes });
    } catch (error) {
      console.error('Error checking biometry support:', error);
      setBiometryType('Device PIN');
      setIsBiometricSupported(false);
      setIsEnrolled(false);
    }
  };

  const handleAuthenticate = async () => {
    setIsLoading(true);

    try {
      // Double-check biometric availability
      if (!isBiometricSupported || !isEnrolled) {
        Alert.alert(
          'Biometric Authentication Unavailable',
          `${biometryType} is not available or not set up on this device. Please set up biometric authentication in your device settings.`,
          [
            {
              text: 'Skip for Demo',
              onPress: () => {
                onAuthenticationSuccess();
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        setIsLoading(false);
        return;
      }

      // Attempt biometric authentication with improved options
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: `Use ${biometryType} to access MyDocs Vault`,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Device PIN',
        disableDeviceFallback: false, // Allow fallback to device PIN
        requireConfirmation: false, // Don't require additional confirmation
      });

      console.log('Authentication result:', result);

      if (result.success) {
        onAuthenticationSuccess();
      } else {
        // Handle different error types
        let errorMessage = 'Authentication failed. Please try again.';

        if (result.error === 'user_cancel') {
          errorMessage = 'Authentication was cancelled.';
        } else if (result.error === 'lockout') {
          errorMessage = 'Too many failed attempts. Please try again later.';
        } else if (result.error === 'not_available') {
          errorMessage = 'Biometric authentication is not available.';
        }

        Alert.alert(
          'Authentication Failed',
          errorMessage,
          [
            {
              text: 'Retry',
              onPress: handleAuthenticate,
            },
            {
              text: 'Skip for Demo',
              onPress: () => {
                onAuthenticationSuccess();
              },
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
      }
    } catch (error) {
      console.error('Authentication error:', error);
      Alert.alert(
        'Authentication Error',
        'An unexpected error occurred during authentication. You can skip for demo purposes.',
        [
          {
            text: 'Skip for Demo',
            onPress: () => {
              onAuthenticationSuccess();
            },
          },
          {
            text: 'Retry',
            onPress: handleAuthenticate,
          },
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getBiometryIcon = () => {
    switch (biometryType) {
      case 'Touch ID':
        return '👆';
      case 'Face ID':
        return '👤';
      case 'Biometric':
        return '🔐';
      default:
        return '🔒';
    }
  };

  const styles = getStyles(isDarkMode);

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />

      {/* Animated Background */}
      <LinearGradient
        colors={isDarkMode
          ? ['#0a0a0a', '#1a1a2e', '#16213e', '#0f3460']
          : ['#667eea', '#764ba2', '#f093fb', '#f5576c']
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.backgroundGradient}
      />

      {/* Floating particles effect */}
      <View style={styles.particlesContainer}>
        {[...Array(6)].map((_, i) => (
          <View key={i} style={[styles.particle, {
            top: `${Math.random() * 80 + 10}%`,
            left: `${Math.random() * 80 + 10}%`,
            animationDelay: `${Math.random() * 3}s`
          }]} />
        ))}
      </View>

      <View style={styles.content}>
        {/* App Logo Section with Liquid Glass */}
        <LiquidGlass style={styles.logoGlass} variant="primary" intensity="medium">
          <View style={styles.logoContainer}>
            <Text style={styles.logoIcon}>🗂️</Text>
            <Text style={styles.appTitle}>MyDocs Vault</Text>
            <Text style={styles.appSubtitle}>Secure Document Storage</Text>
          </View>
        </LiquidGlass>

        {/* Authentication Section with Liquid Glass */}
        <LiquidGlass style={styles.authGlass} variant="secondary" intensity="strong">
          <View style={styles.authSection}>
            {/* Biometric Icon with Glass Effect */}
            <LiquidGlass style={styles.biometryIconGlass} variant="accent" intensity="light">
              <View style={styles.biometryIconContainer}>
                <Text style={styles.biometryIcon}>{getBiometryIcon()}</Text>
              </View>
            </LiquidGlass>

            <Text style={styles.authTitle}>
              Secure Access Required
            </Text>

            <Text style={styles.authDescription}>
              {isBiometricSupported && isEnrolled
                ? `Use ${biometryType} to unlock your secure documents`
                : `${biometryType} authentication to access your documents`
              }
            </Text>

            <LiquidGlass style={styles.buttonGlass} variant="primary" intensity="medium">
              <Button
                mode="contained"
                onPress={handleAuthenticate}
                disabled={isLoading}
                style={styles.authButton}
                contentStyle={styles.authButtonContent}
                labelStyle={styles.authButtonLabel}>
                {isLoading ? (
                  <ActivityIndicator color="#ffffff" size="small" />
                ) : (
                  `Authenticate with ${biometryType}`
                )}
              </Button>
            </LiquidGlass>
          </View>
        </LiquidGlass>

        {/* Security Notice with Liquid Glass */}
        <LiquidGlass style={styles.securityGlass} variant="secondary" intensity="light">
          <View style={styles.securityNotice}>
            <Text style={styles.securityText}>
              🔒 Your documents are encrypted and stored securely on this device
            </Text>
          </View>
        </LiquidGlass>
      </View>
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    backgroundGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    particlesContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    particle: {
      position: 'absolute',
      width: 4,
      height: 4,
      borderRadius: 2,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      opacity: 0.6,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingVertical: 40,
    },
    logoGlass: {
      marginBottom: 40,
      paddingHorizontal: 32,
      paddingVertical: 24,
      width: width * 0.85,
    },
    logoContainer: {
      alignItems: 'center',
    },
    logoIcon: {
      fontSize: 64,
      marginBottom: 12,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    appTitle: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#ffffff',
      marginBottom: 8,
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.5)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    appSubtitle: {
      fontSize: 16,
      color: 'rgba(255, 255, 255, 0.9)',
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    authGlass: {
      marginBottom: 32,
      paddingHorizontal: 24,
      paddingVertical: 32,
      width: width * 0.9,
    },
    authSection: {
      alignItems: 'center',
      width: '100%',
    },
    biometryIconGlass: {
      marginBottom: 24,
      padding: 16,
    },
    biometryIconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
    biometryIcon: {
      fontSize: 48,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    authTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: '#ffffff',
      marginBottom: 12,
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.5)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    authDescription: {
      fontSize: 16,
      color: 'rgba(255, 255, 255, 0.85)',
      textAlign: 'center',
      marginBottom: 32,
      lineHeight: 22,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    buttonGlass: {
      width: '100%',
      borderRadius: 16,
    },
    authButton: {
      backgroundColor: 'transparent',
      borderRadius: 16,
      width: '100%',
    },
    authButtonContent: {
      paddingVertical: 12,
    },
    authButtonLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: '#ffffff',
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    securityGlass: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      width: width * 0.85,
    },
    securityNotice: {
      alignItems: 'center',
    },
    securityText: {
      fontSize: 14,
      color: 'rgba(255, 255, 255, 0.8)',
      textAlign: 'center',
      lineHeight: 20,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
  });

export default AuthenticationScreen;
