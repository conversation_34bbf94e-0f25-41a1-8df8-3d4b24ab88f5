import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';

interface Props {
  onAuthenticationSuccess: () => void;
}

const AuthenticationScreen: React.FC<Props> = ({ onAuthenticationSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleGetStarted = () => {
    setIsLoading(true);
    // Simple delay to show loading state, then proceed
    setTimeout(() => {
      setIsLoading(false);
      onAuthenticationSuccess();
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Modern Gradient Background */}
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.backgroundGradient}
      />

      <View style={styles.content}>
        {/* Main Character/Avatar */}
        <View style={styles.avatarContainer}>
          <View style={styles.avatarCircle}>
            <Text style={styles.avatarEmoji}>📁</Text>
            <View style={styles.avatarFace}>
              <View style={styles.eyesContainer}>
                <View style={[styles.eye, { left: -12 }]} />
                <View style={[styles.eye, { right: -12 }]} />
              </View>
              <View style={styles.smile} />
            </View>
          </View>
        </View>

        {/* Welcome Text */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Welcome to</Text>
          <Text style={styles.appName}>MyDocs Vault</Text>
          <Text style={styles.welcomeSubtitle}>
            Your secure document storage companion
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonSection}>
          <TouchableOpacity
            style={[styles.primaryButton, isLoading && styles.buttonDisabled]}
            onPress={handleGetStarted}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#ffffff" size="small" />
            ) : (
              <>
                <Text style={styles.primaryButtonText}>Get Started</Text>
                <Text style={styles.buttonArrow}>→</Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Learn More</Text>
          </TouchableOpacity>
        </View>

        {/* Features Preview */}
        <View style={styles.featuresSection}>
          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>🔒</Text>
            </View>
            <Text style={styles.featureText}>Secure Storage</Text>
          </View>

          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>📱</Text>
            </View>
            <Text style={styles.featureText}>Easy Access</Text>
          </View>

          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>☁️</Text>
            </View>
            <Text style={styles.featureText}>Cloud Sync</Text>
          </View>
        </View>

        {/* Bottom Indicator */}
        <View style={styles.bottomIndicator}>
          <View style={styles.indicatorDot} />
          <View style={[styles.indicatorDot, styles.activeDot]} />
          <View style={styles.indicatorDot} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000',
    },
    backgroundGradient: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: '100%',
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingTop: 80,
      paddingBottom: 60,
    },
    avatarContainer: {
      marginBottom: 48,
      alignItems: 'center',
    },
    avatarCircle: {
      width: 160,
      height: 160,
      borderRadius: 80,
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 8,
    },
    avatarEmoji: {
      fontSize: 64,
      position: 'absolute',
      top: 20,
    },
    avatarFace: {
      position: 'absolute',
      bottom: 30,
      alignItems: 'center',
    },
    eyesContainer: {
      flexDirection: 'row',
      width: 32,
      height: 8,
      position: 'relative',
    },
    eye: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#333',
      position: 'absolute',
    },
    smile: {
      width: 24,
      height: 12,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: '#333',
      borderTopColor: 'transparent',
      marginTop: 16,
    },
    welcomeSection: {
      alignItems: 'center',
      marginBottom: 48,
    },
    welcomeTitle: {
      fontSize: 24,
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: '400',
      marginBottom: 8,
    },
    appName: {
      fontSize: 36,
      color: '#ffffff',
      fontWeight: '700',
      marginBottom: 12,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    welcomeSubtitle: {
      fontSize: 16,
      color: 'rgba(255, 255, 255, 0.7)',
      textAlign: 'center',
      lineHeight: 22,
      paddingHorizontal: 20,
    },
    buttonSection: {
      width: '100%',
      marginBottom: 40,
    },
    primaryButton: {
      backgroundColor: '#ffffff',
      borderRadius: 25,
      paddingVertical: 16,
      paddingHorizontal: 32,
      marginBottom: 16,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
    buttonDisabled: {
      opacity: 0.7,
    },
    primaryButtonText: {
      fontSize: 18,
      fontWeight: '600',
      color: '#333',
      marginRight: 8,
    },
    buttonArrow: {
      fontSize: 18,
      color: '#333',
      fontWeight: '600',
    },
    secondaryButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderRadius: 25,
      paddingVertical: 16,
      paddingHorizontal: 32,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.3)',
    },
    secondaryButtonText: {
      fontSize: 16,
      fontWeight: '500',
      color: '#ffffff',
      textAlign: 'center',
    },
    featuresSection: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      width: '100%',
      marginBottom: 40,
    },
    featureCard: {
      alignItems: 'center',
      flex: 1,
      marginHorizontal: 8,
    },
    featureIcon: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 12,
    },
    featureEmoji: {
      fontSize: 24,
    },
    featureText: {
      fontSize: 12,
      color: 'rgba(255, 255, 255, 0.8)',
      textAlign: 'center',
      fontWeight: '500',
    },
    bottomIndicator: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    indicatorDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      marginHorizontal: 4,
    },
    activeDot: {
      backgroundColor: '#ffffff',
      width: 24,
      borderRadius: 4,
    },
  });

export default AuthenticationScreen;
