import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as LocalAuthentication from 'expo-local-authentication';

interface Props {
  onAuthenticationSuccess: () => void;
}

const AuthenticationScreen: React.FC<Props> = ({ onAuthenticationSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    setIsLoading(true);

    try {
      // Use device authentication (Face ID/Touch ID/Passcode)
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Unlock MyDocs Vault',
        fallbackLabel: 'Use Passcode',
        disableDeviceFallback: false,
      });

      if (result.success) {
        setTimeout(() => {
          setIsLoading(false);
          onAuthenticationSuccess();
        }, 500);
      } else {
        setIsLoading(false);
        Alert.alert('Authentication Failed', 'Please try again');
      }
    } catch (error) {
      setIsLoading(false);
      Alert.alert('Error', 'Authentication failed. Please try again.');
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Modern Gradient Background */}
      <LinearGradient
        colors={['#000000', '#1a1a2e', '#16213e', '#0f3460']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.backgroundGradient}
      />

      <View style={styles.content}>
        {/* Main Character/Avatar */}
        <View style={styles.avatarContainer}>
          <View style={styles.avatarCircle}>
            <Text style={styles.lockIcon}>🔐</Text>
            <View style={styles.securityRing}>
              <View style={styles.innerRing} />
            </View>
          </View>
        </View>

        {/* Welcome Text */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Welcome to</Text>
          <Text style={styles.appName}>MyDocs Vault</Text>
          <Text style={styles.welcomeSubtitle}>
            Tap "Get In" to authenticate with Face ID or Passcode
          </Text>
        </View>

        {/* Unlock Button */}
        <View style={styles.buttonSection}>
          <TouchableOpacity
            style={[styles.primaryButton, isLoading && styles.buttonDisabled]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#ffffff" size="small" />
            ) : (
              <>
                <Text style={styles.primaryButtonText}>🔓 Get In</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        {/* Bottom Indicator */}
        <View style={styles.bottomIndicator}>
          <View style={styles.indicatorDot} />
          <View style={[styles.indicatorDot, styles.activeDot]} />
          <View style={styles.indicatorDot} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000',
    },
    backgroundGradient: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: '100%',
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingTop: 80,
      paddingBottom: 60,
    },
    avatarContainer: {
      marginBottom: 48,
      alignItems: 'center',
    },
    avatarCircle: {
      width: 160,
      height: 160,
      borderRadius: 80,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 2,
      borderColor: '#2196F3',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      shadowColor: '#2196F3',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 8,
    },
    lockIcon: {
      fontSize: 72,
      position: 'absolute',
      top: 50,
      left: 0,
      right: 0,
      textAlign: 'center',
    },
    securityRing: {
      position: 'absolute',
      bottom: 20,
      left: 20,
      right: 20,
      height: 4,
      backgroundColor: 'rgba(33, 150, 243, 0.3)',
      borderRadius: 2,
      overflow: 'hidden',
    },
    innerRing: {
      height: '100%',
      width: '70%',
      backgroundColor: '#2196F3',
      borderRadius: 2,
    },
    welcomeSection: {
      alignItems: 'center',
      marginBottom: 32,
    },
    welcomeTitle: {
      fontSize: 24,
      color: 'rgba(255, 255, 255, 0.8)',
      fontWeight: '400',
      marginBottom: 8,
    },
    appName: {
      fontSize: 36,
      color: '#ffffff',
      fontWeight: '700',
      marginBottom: 12,
      textShadowColor: 'rgba(33, 150, 243, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    welcomeSubtitle: {
      fontSize: 16,
      color: 'rgba(255, 255, 255, 0.7)',
      textAlign: 'center',
      lineHeight: 22,
      paddingHorizontal: 20,
    },

    buttonSection: {
      width: '100%',
      marginBottom: 40,
    },
    primaryButton: {
      backgroundColor: '#2196F3',
      borderRadius: 25,
      paddingVertical: 16,
      paddingHorizontal: 32,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#2196F3',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
    buttonDisabled: {
      opacity: 0.7,
    },
    primaryButtonText: {
      fontSize: 18,
      fontWeight: '600',
      color: '#ffffff',
    },
    bottomIndicator: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    indicatorDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      marginHorizontal: 4,
    },
    activeDot: {
      backgroundColor: '#2196F3',
      width: 24,
      borderRadius: 4,
    },
  });

export default AuthenticationScreen;
