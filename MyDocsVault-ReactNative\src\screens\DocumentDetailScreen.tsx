import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  useColorScheme,
  ScrollView,
  Linking,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  IconButton,
  ActivityIndicator,
} from 'react-native-paper';
import Share from 'react-native-share';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';

import {Document, CATEGORY_INFO} from '../types/Document';
import {DocumentService} from '../services/DocumentService';
import {RootStackParamList} from '../../App';

type DocumentDetailNavigationProp = StackNavigationProp<
  RootStackParamList,
  'DocumentDetail'
>;

type DocumentDetailRouteProp = RouteProp<RootStackParamList, 'DocumentDetail'>;

interface Props {
  navigation: DocumentDetailNavigationProp;
  route: DocumentDetailRouteProp;
}

const DocumentDetailScreen: React.FC<Props> = ({navigation, route}) => {
  const isDarkMode = useColorScheme() === 'dark';
  const {documentId} = route.params;
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDocument();
  }, [documentId]);

  const loadDocument = async () => {
    try {
      const doc = await DocumentService.getDocumentById(documentId);
      setDocument(doc);
    } catch (error) {
      console.error('Error loading document:', error);
      Alert.alert('Error', 'Failed to load document');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDocument = async () => {
    if (!document) return;

    try {
      // Try to open the document with the default app
      const supported = await Linking.canOpenURL(document.filePath);
      
      if (supported) {
        await Linking.openURL(document.filePath);
      } else {
        Alert.alert(
          'Cannot Open',
          'No app available to open this document type',
        );
      }
    } catch (error) {
      console.error('Error opening document:', error);
      Alert.alert('Error', 'Failed to open document');
    }
  };

  const handleShareDocument = async () => {
    if (!document) return;

    try {
      const shareOptions = {
        title: 'Share Document',
        message: `Sharing: ${document.name}`,
        url: `file://${document.filePath}`,
        type: document.fileExtension === 'pdf' ? 'application/pdf' : 'image/*',
      };

      await Share.open(shareOptions);
    } catch (error) {
      if (error.message !== 'User did not share') {
        console.error('Error sharing document:', error);
        Alert.alert('Error', 'Failed to share document');
      }
    }
  };

  const handleDeleteDocument = () => {
    if (!document) return;

    Alert.alert(
      'Delete Document',
      `Are you sure you want to delete "${document.name}"? This action cannot be undone.`,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await DocumentService.deleteDocument(document.id);
            if (success) {
              navigation.goBack();
            } else {
              Alert.alert('Error', 'Failed to delete document');
            }
          },
        },
      ],
    );
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const styles = getStyles(isDarkMode);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading document...</Text>
      </View>
    );
  }

  if (!document) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Document not found</Text>
        <Button onPress={() => navigation.goBack()}>Go Back</Button>
      </View>
    );
  }

  const categoryInfo = CATEGORY_INFO[document.category];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Document Header */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.header}>
            <View style={styles.headerInfo}>
              <Title style={styles.documentTitle}>{document.name}</Title>
              <Paragraph style={styles.documentCategory}>
                {categoryInfo.category}
              </Paragraph>
            </View>
            <IconButton
              icon="delete"
              size={24}
              onPress={handleDeleteDocument}
              iconColor="#FF3B30"
            />
          </View>
        </Card.Content>
      </Card>

      {/* Document Preview */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Preview</Title>
          <View style={styles.previewContainer}>
            <Text style={styles.previewIcon}>
              {document.fileExtension === 'pdf' ? '📄' : '🖼️'}
            </Text>
            <Text style={styles.fileName}>{document.fileName}</Text>
            <Button
              mode="contained"
              onPress={handleOpenDocument}
              style={styles.openButton}>
              Open Document
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Document Information */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Information</Title>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>File Size:</Text>
            <Text style={styles.infoValue}>
              {DocumentService.formatFileSize(document.fileSize)}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>File Type:</Text>
            <Text style={styles.infoValue}>
              {document.fileExtension.toUpperCase()}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoValue}>
              {formatDate(document.dateCreated)}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Modified:</Text>
            <Text style={styles.infoValue}>
              {formatDate(document.dateModified)}
            </Text>
          </View>
          
          {document.description && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Description:</Text>
              <Text style={styles.infoValue}>{document.description}</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Actions */}
      <Card style={styles.card}>
        <Card.Content>
          <Title>Actions</Title>
          
          <Button
            mode="contained"
            icon="share"
            onPress={handleShareDocument}
            style={styles.actionButton}>
            Share Document
          </Button>
          
          <Button
            mode="outlined"
            icon="pencil"
            onPress={() => {
              // Navigate to edit screen (to be implemented)
              Alert.alert('Coming Soon', 'Edit functionality will be available soon');
            }}
            style={styles.actionButton}>
            Edit Details
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    content: {
      padding: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 8,
      fontSize: 16,
      color: isDarkMode ? '#ffffff' : '#000000',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    errorText: {
      fontSize: 18,
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 16,
    },
    card: {
      marginBottom: 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    headerInfo: {
      flex: 1,
    },
    documentTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 4,
    },
    documentCategory: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
    },
    previewContainer: {
      alignItems: 'center',
      paddingVertical: 20,
    },
    previewIcon: {
      fontSize: 64,
      marginBottom: 12,
    },
    fileName: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      marginBottom: 16,
      textAlign: 'center',
    },
    openButton: {
      backgroundColor: '#007AFF',
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: isDarkMode ? '#333333' : '#eeeeee',
    },
    infoLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: isDarkMode ? '#ffffff' : '#000000',
      flex: 1,
    },
    infoValue: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      flex: 2,
      textAlign: 'right',
    },
    actionButton: {
      marginBottom: 12,
    },
  });

export default DocumentDetailScreen;
