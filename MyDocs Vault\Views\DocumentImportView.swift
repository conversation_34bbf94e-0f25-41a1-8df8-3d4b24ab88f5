//
//  DocumentImportView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI
import PhotosUI
import UniformTypeIdentifiers

struct DocumentImportView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var documentManager: DocumentManager
    
    @State private var documentName = ""
    @State private var selectedCategory: DocumentCategory = .other
    @State private var showingDocumentPicker = false
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var isImporting = false
    @State private var importError: String?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Add New Document")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // Import Options
                VStack(spacing: 16) {
                    ImportOptionButton(
                        title: "Files App",
                        subtitle: "Import from Files",
                        icon: "folder",
                        color: .blue
                    ) {
                        showingDocumentPicker = true
                    }
                    
                    ImportOptionButton(
                        title: "Camera",
                        subtitle: "Take a photo",
                        icon: "camera",
                        color: .green
                    ) {
                        showingCamera = true
                    }
                    
                    ImportOptionButton(
                        title: "Photo Library",
                        subtitle: "Choose from photos",
                        icon: "photo.on.rectangle",
                        color: .purple
                    ) {
                        showingImagePicker = true
                    }
                    
                    ImportOptionButton(
                        title: "Text Note",
                        subtitle: "Create a new note",
                        icon: "note.text",
                        color: .orange
                    ) {
                        createTextNote()
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                if isImporting {
                    ProgressView("Importing document...")
                        .padding()
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .fileImporter(
                isPresented: $showingDocumentPicker,
                allowedContentTypes: [.pdf, .image, .text, .plainText],
                allowsMultipleSelection: false
            ) { result in
                handleFileImport(result: result)
            }
            .photosPicker(
                isPresented: $showingImagePicker,
                selection: $selectedPhotoItem,
                matching: .images
            )
            .fullScreenCover(isPresented: $showingCamera) {
                CameraView { image in
                    handleCameraImage(image)
                }
            }
            .onChange(of: selectedPhotoItem) { photoItem in
                if let photoItem = photoItem {
                    handlePhotoSelection(photoItem)
                }
            }
            .alert("Import Error", isPresented: .constant(importError != nil)) {
                Button("OK") {
                    importError = nil
                }
            } message: {
                Text(importError ?? "")
            }
        }
    }
    
    private func handleFileImport(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            showDocumentDetailsSheet(for: url)
        case .failure(let error):
            importError = "Failed to import file: \(error.localizedDescription)"
        }
    }
    
    private func handlePhotoSelection(_ photoItem: PhotosPickerItem) {
        Task {
            if let data = try? await photoItem.loadTransferable(type: Data.self) {
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(UUID().uuidString).jpg")
                try? data.write(to: tempURL)
                await MainActor.run {
                    showDocumentDetailsSheet(for: tempURL)
                }
            }
        }
    }
    
    private func handleCameraImage(_ image: UIImage) {
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(UUID().uuidString).jpg")
        try? data.write(to: tempURL)
        showDocumentDetailsSheet(for: tempURL)
    }
    
    private func createTextNote() {
        let content = "New Text Note\n\nStart typing your note here..."
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("\(UUID().uuidString).txt")
        try? content.write(to: tempURL, atomically: true, encoding: .utf8)
        showDocumentDetailsSheet(for: tempURL)
    }
    
    private func showDocumentDetailsSheet(for url: URL) {
        // This would typically show a sheet for entering document details
        // For now, we'll use default values and import directly
        let fileName = url.lastPathComponent
        let nameWithoutExtension = url.deletingPathExtension().lastPathComponent
        
        Task {
            isImporting = true
            let success = await documentManager.addDocument(
                from: url,
                name: nameWithoutExtension,
                category: .other
            )
            
            isImporting = false
            
            if success {
                dismiss()
            } else {
                importError = "Failed to import document"
            }
        }
    }
}

struct ImportOptionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 40)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct CameraView: UIViewControllerRepresentable {
    let onImageCaptured: (UIImage) -> Void
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = .camera
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView
        
        init(_ parent: CameraView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageCaptured(image)
            }
            parent.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

#Preview {
    DocumentImportView()
        .environmentObject(DocumentManager())
}
