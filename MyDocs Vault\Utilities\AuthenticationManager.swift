//
//  AuthenticationManager.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import Foundation
import LocalAuthentication
import SwiftUI

@MainActor
class AuthenticationManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var authenticationError: String?
    @Published var biometryType: LABiometryType = .none
    
    private let context = LAContext()
    
    init() {
        checkBiometryAvailability()
    }
    
    func checkBiometryAvailability() {
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            biometryType = context.biometryType
        } else {
            biometryType = .none
        }
    }
    
    func authenticate() async {
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil) ||
              context.canEvaluatePolicy(.deviceOwnerAuthentication, error: nil) else {
            authenticationError = "Biometric authentication is not available on this device."
            return
        }
        
        let reason = "Authenticate to access your secure documents"
        
        do {
            let success = try await context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason)
            
            if success {
                isAuthenticated = true
                authenticationError = nil
            }
        } catch let error as LAError {
            handleAuthenticationError(error)
        } catch {
            authenticationError = "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
    
    func authenticateWithPasscode() async {
        let reason = "Authenticate to access your secure documents"
        
        do {
            let success = try await context.evaluatePolicy(.deviceOwnerAuthentication, localizedReason: reason)
            
            if success {
                isAuthenticated = true
                authenticationError = nil
            }
        } catch let error as LAError {
            handleAuthenticationError(error)
        } catch {
            authenticationError = "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
    
    private func handleAuthenticationError(_ error: LAError) {
        switch error.code {
        case .authenticationFailed:
            authenticationError = "Authentication failed. Please try again."
        case .userCancel:
            authenticationError = "Authentication was cancelled."
        case .userFallback:
            authenticationError = "User chose to enter passcode."
        case .biometryNotAvailable:
            authenticationError = "Biometric authentication is not available."
        case .biometryNotEnrolled:
            authenticationError = "Biometric authentication is not set up."
        case .biometryLockout:
            authenticationError = "Biometric authentication is locked. Please use passcode."
        case .appCancel:
            authenticationError = "Authentication was cancelled by the app."
        case .invalidContext:
            authenticationError = "Authentication context is invalid."
        case .notInteractive:
            authenticationError = "Authentication is not interactive."
        case .passcodeNotSet:
            authenticationError = "Passcode is not set on this device."
        case .systemCancel:
            authenticationError = "Authentication was cancelled by the system."
        case .touchIDLockout:
            authenticationError = "Touch ID is locked. Please use passcode."
        case .touchIDNotAvailable:
            authenticationError = "Touch ID is not available."
        case .touchIDNotEnrolled:
            authenticationError = "Touch ID is not enrolled."
        case .watchNotAvailable:
            authenticationError = "Apple Watch is not available."
        case .biometryDisconnected:
            authenticationError = "Biometric sensor is disconnected."
        default:
            authenticationError = "Authentication failed: \(error.localizedDescription)"
        }
    }
    
    func logout() {
        isAuthenticated = false
        authenticationError = nil
    }
    
    var biometryTypeString: String {
        switch biometryType {
        case .faceID:
            return "Face ID"
        case .touchID:
            return "Touch ID"
        case .opticID:
            return "Optic ID"
        case .none:
            return "Passcode"
        @unknown default:
            return "Biometric"
        }
    }
    
    var biometryIcon: String {
        switch biometryType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        case .opticID:
            return "opticid"
        case .none:
            return "lock"
        @unknown default:
            return "lock"
        }
    }
}
