export interface Document {
  id: string;
  name: string;
  fileName: string;
  fileExtension: string;
  category: DocumentCategory;
  dateCreated: Date;
  dateModified: Date;
  fileSize: number;
  thumbnailPath?: string;
  filePath: string;
  description?: string;
}

export enum DocumentCategory {
  IDS = 'IDs',
  BILLS = 'Bills',
  CERTIFICATES = 'Certificates',
  PERSONAL_NOTES = 'Personal Notes',
  OTHER = 'Other',
}

export interface DocumentMetadata {
  id: string;
  name: string;
  fileName: string;
  fileExtension: string;
  category: DocumentCategory;
  dateCreated: string;
  dateModified: string;
  fileSize: number;
  thumbnailPath?: string;
  filePath: string;
  description?: string;
}

export interface ImportResult {
  success: boolean;
  document?: Document;
  error?: string;
}

export interface AuthenticationResult {
  success: boolean;
  error?: string;
  biometryType?: string;
}

export interface CategoryInfo {
  category: DocumentCategory;
  count: number;
  icon: string;
  color: string;
}

export const CATEGORY_INFO: Record<DocumentCategory, Omit<CategoryInfo, 'count'>> = {
  [DocumentCategory.IDS]: {
    category: DocumentCategory.IDS,
    icon: 'account-card-details',
    color: '#FF6B6B',
  },
  [DocumentCategory.BILLS]: {
    category: DocumentCategory.BILLS,
    icon: 'receipt',
    color: '#4ECDC4',
  },
  [DocumentCategory.CERTIFICATES]: {
    category: DocumentCategory.CERTIFICATES,
    icon: 'certificate',
    color: '#45B7D1',
  },
  [DocumentCategory.PERSONAL_NOTES]: {
    category: DocumentCategory.PERSONAL_NOTES,
    icon: 'note-text',
    color: '#96CEB4',
  },
  [DocumentCategory.OTHER]: {
    category: DocumentCategory.OTHER,
    icon: 'folder',
    color: '#FFEAA7',
  },
};

export const SUPPORTED_FILE_TYPES = [
  'pdf',
  'jpg',
  'jpeg',
  'png',
  'heic',
  'txt',
  'md',
  'rtf',
];

export const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'heic'];
export const PDF_EXTENSIONS = ['pdf'];
export const TEXT_EXTENSIONS = ['txt', 'md', 'rtf'];
