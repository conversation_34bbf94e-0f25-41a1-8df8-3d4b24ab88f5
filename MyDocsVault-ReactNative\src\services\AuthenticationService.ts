import ReactNativeBiometrics, {BiometryTypes} from 'react-native-biometrics';
import {AuthenticationResult} from '../types/Document';

export class AuthenticationService {
  private static rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });

  /**
   * Check if biometric authentication is available on the device
   */
  static async isBiometricAvailable(): Promise<boolean> {
    try {
      const {available} = await this.rnBiometrics.isSensorAvailable();
      return available;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  /**
   * Get the type of biometric authentication available
   */
  static async getBiometryType(): Promise<BiometryTypes | null> {
    try {
      const {available, biometryType} = await this.rnBiometrics.isSensorAvailable();
      return available ? biometryType : null;
    } catch (error) {
      console.error('Error getting biometry type:', error);
      return null;
    }
  }

  /**
   * Authenticate user using biometrics or device credentials
   */
  static async authenticate(): Promise<AuthenticationResult> {
    try {
      const {available, biometryType} = await this.rnBiometrics.isSensorAvailable();
      
      if (!available) {
        return {
          success: false,
          error: 'Biometric authentication not available',
        };
      }

      const promptMessage = this.getPromptMessage(biometryType);
      
      const {success} = await this.rnBiometrics.simplePrompt({
        promptMessage,
        cancelButtonText: 'Cancel',
      });

      if (success) {
        return {
          success: true,
          biometryType: biometryType || undefined,
        };
      } else {
        return {
          success: false,
          error: 'Authentication was cancelled or failed',
        };
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Get appropriate prompt message based on biometry type
   */
  private static getPromptMessage(biometryType: BiometryTypes | null): string {
    switch (biometryType) {
      case BiometryTypes.TouchID:
        return 'Use Touch ID to access your secure documents';
      case BiometryTypes.FaceID:
        return 'Use Face ID to access your secure documents';
      case BiometryTypes.Biometrics:
        return 'Use biometric authentication to access your secure documents';
      default:
        return 'Authenticate to access your secure documents';
    }
  }

  /**
   * Check if device credentials (PIN/Pattern/Password) are available
   */
  static async isDeviceCredentialAvailable(): Promise<boolean> {
    try {
      // This is a fallback check - most modern devices support this
      return true;
    } catch (error) {
      console.error('Error checking device credentials:', error);
      return false;
    }
  }

  /**
   * Create biometric keys for enhanced security (optional)
   */
  static async createBiometricKeys(): Promise<boolean> {
    try {
      const {available} = await this.rnBiometrics.isSensorAvailable();
      
      if (!available) {
        return false;
      }

      const {keysExist} = await this.rnBiometrics.biometricKeysExist();
      
      if (!keysExist) {
        const {publicKey} = await this.rnBiometrics.createKeys();
        console.log('Biometric keys created:', publicKey);
        return true;
      }
      
      return true;
    } catch (error) {
      console.error('Error creating biometric keys:', error);
      return false;
    }
  }

  /**
   * Delete biometric keys
   */
  static async deleteBiometricKeys(): Promise<boolean> {
    try {
      const {keysDeleted} = await this.rnBiometrics.deleteKeys();
      return keysDeleted;
    } catch (error) {
      console.error('Error deleting biometric keys:', error);
      return false;
    }
  }
}
