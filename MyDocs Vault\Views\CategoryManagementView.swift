//
//  CategoryManagementView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI

struct CategoryManagementView: View {
    @EnvironmentObject var documentManager: DocumentManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(DocumentCategory.allCases, id: \.self) { category in
                    CategoryRowView(category: category)
                }
            }
            .navigationTitle("Categories")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CategoryRowView: View {
    let category: DocumentCategory
    @EnvironmentObject var documentManager: DocumentManager
    
    private var documentCount: Int {
        documentManager.documentsInCategory(category).count
    }
    
    var body: some View {
        HStack(spacing: 16) {
            // Category Icon
            ZStack {
                Circle()
                    .fill(category.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: category.systemImageName)
                    .foregroundColor(category.color)
                    .font(.system(size: 18, weight: .medium))
            }
            
            // Category Info
            VStack(alignment: .leading, spacing: 4) {
                Text(category.displayName)
                    .font(.headline)
                
                Text("\(documentCount) document\(documentCount == 1 ? "" : "s")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Document count badge
            if documentCount > 0 {
                Text("\(documentCount)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(category.color)
                    .clipShape(Capsule())
            }
        }
        .padding(.vertical, 4)
    }
}

struct DocumentCategoryPicker: View {
    @Binding var selectedCategory: DocumentCategory
    let title: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(DocumentCategory.allCases, id: \.self) { category in
                    CategoryPickerButton(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
        }
    }
}

struct CategoryPickerButton: View {
    let category: DocumentCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .fill(isSelected ? category.color : category.color.opacity(0.2))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: category.systemImageName)
                        .foregroundColor(isSelected ? .white : category.color)
                        .font(.system(size: 20, weight: .medium))
                }
                
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .regular)
                    .foregroundColor(isSelected ? category.color : .primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? category.color.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? category.color : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    CategoryManagementView()
        .environmentObject(DocumentManager())
}
