//
//  ContentView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var authManager: AuthenticationManager
    @EnvironmentObject var documentManager: DocumentManager
    @State private var showingAuthenticationView = true
    
    var body: some View {
        Group {
            if authManager.isAuthenticated {
                DocumentListView()
            } else {
                AuthenticationView()
            }
        }
        .animation(.easeInOut, value: authManager.isAuthenticated)
    }
}

struct AuthenticationView: View {
    @EnvironmentObject var authManager: AuthenticationManager
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // App Icon and Title
            VStack(spacing: 20) {
                Image(systemName: "lock.shield.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                Text("MyDocs Vault")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Secure Document Storage")
                    .font(.title3)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Authentication Section
            VStack(spacing: 20) {
                if let errorMessage = authManager.authenticationError {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(10)
                }
                
                Button(action: {
                    Task {
                        await authManager.authenticate()
                    }
                }) {
                    HStack {
                        Image(systemName: authManager.biometryIcon)
                            .font(.title2)
                        Text("Unlock with \(authManager.biometryTypeString)")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                
                if authManager.biometryType != .none {
                    Button(action: {
                        Task {
                            await authManager.authenticateWithPasscode()
                        }
                    }) {
                        Text("Use Passcode")
                            .font(.headline)
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(.horizontal, 40)
            
            Spacer()
            
            Text("Your documents are protected with biometric authentication")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .padding(.bottom, 40)
        }
        .background(Color(.systemBackground))
    }
}

#Preview {
    ContentView()
        .environmentObject(AuthenticationManager())
        .environmentObject(DocumentManager())
}
