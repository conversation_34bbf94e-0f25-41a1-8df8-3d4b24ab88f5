# 🚀 MyDocs Vault - Cross-Platform Setup Guide

This guide will help you set up both React Native and Flutter development environments on Windows to build MyDocs Vault for iOS and Android.

## 📋 Prerequisites

- **Windows 10/11** with administrator access
- **8GB+ RAM** recommended
- **20GB+ free disk space**
- **Stable internet connection**

## 🔧 Common Setup (Required for Both)

### 1. Install Node.js
```bash
# Download from https://nodejs.org (LTS version)
# Or use Chocolatey
choco install nodejs
```

### 2. Install Git
```bash
# Download from https://git-scm.com
# Or use Chocolatey
choco install git
```

### 3. Install Android Studio
1. Download from [developer.android.com/studio](https://developer.android.com/studio)
2. Install with default settings
3. Open Android Studio and complete setup wizard
4. Install Android SDK (API 33 recommended)

### 4. Set Environment Variables
Add to your system PATH:
```
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\tools
```

## ⚛️ React Native Setup

### 1. Install React Native CLI
```bash
npm install -g react-native-cli
npm install -g @react-native-community/cli
```

### 2. Install Java Development Kit (JDK)
```bash
# Install JDK 11 or 17
choco install openjdk11
```

### 3. Verify Installation
```bash
npx react-native doctor
```

## 🔷 Flutter Setup

### 1. Download Flutter SDK
1. Download from [flutter.dev](https://flutter.dev/docs/get-started/install/windows)
2. Extract to `C:\flutter`
3. Add `C:\flutter\bin` to your PATH

### 2. Run Flutter Doctor
```bash
flutter doctor
```

### 3. Accept Android Licenses
```bash
flutter doctor --android-licenses
```

## 📱 Device Setup

### For Android Testing:
1. **Enable Developer Options** on your Android phone
2. **Enable USB Debugging**
3. Connect via USB and authorize computer

### For iOS Testing (Optional):
- Requires Mac for final iOS builds
- Can use online services like Codemagic or Bitrise

## 🎯 Quick Start Commands

### React Native:
```bash
# Create new project
npx react-native init MyDocsVaultRN
cd MyDocsVaultRN

# Run on Android
npx react-native run-android

# Run on iOS (Mac only)
npx react-native run-ios
```

### Flutter:
```bash
# Create new project
flutter create mydocs_vault_flutter
cd mydocs_vault_flutter

# Run on Android
flutter run

# Build APK
flutter build apk
```

## 🔍 Troubleshooting

### Common Issues:
1. **Android SDK not found**: Check PATH variables
2. **Gradle build failed**: Clear cache with `./gradlew clean`
3. **Device not detected**: Check USB debugging and drivers

### Useful Commands:
```bash
# React Native
npx react-native doctor
adb devices

# Flutter
flutter doctor -v
flutter devices
```

## 📦 Required Packages

### React Native Dependencies:
- `react-native-biometrics` - Fingerprint/Face ID
- `react-native-document-picker` - File selection
- `react-native-image-picker` - Camera integration
- `react-native-fs` - File system operations
- `react-native-pdf` - PDF viewing
- `@react-native-async-storage/async-storage` - Local storage

### Flutter Dependencies:
- `local_auth` - Biometric authentication
- `file_picker` - File selection
- `image_picker` - Camera integration
- `path_provider` - File system paths
- `flutter_pdfview` - PDF viewing
- `shared_preferences` - Local storage

## 🚀 Next Steps

1. **Choose your preferred framework** (React Native or Flutter)
2. **Follow the setup instructions** above
3. **Clone the MyDocs Vault project** for your chosen framework
4. **Run the app** on your device or emulator

Both frameworks will give you:
- ✅ Cross-platform compatibility (iOS + Android)
- ✅ Native performance
- ✅ Access to device features (camera, biometrics, file system)
- ✅ Single codebase for both platforms

Ready to start building? Let's create your secure document vault! 🔐
