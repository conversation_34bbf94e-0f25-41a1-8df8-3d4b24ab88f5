{"version": 3, "file": "NativeLinearGradient.ios.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.ios.tsx"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAK7D,MAAM,oBAAoB,GAAG,wBAAwB,CACnD,oBAAoB,CACkB,CAAC;AAEzC,eAAe,oBAAoB,CAAC", "sourcesContent": ["'use client';\n\nimport { requireNativeViewManager } from 'expo-modules-core';\nimport * as React from 'react';\n\nimport { NativeLinearGradientProps } from './NativeLinearGradient.types';\n\nconst NativeLinearGradient = requireNativeViewManager(\n  'ExpoLinearGradient'\n) as React.FC<NativeLinearGradientProps>;\n\nexport default NativeLinearGradient;\n"]}