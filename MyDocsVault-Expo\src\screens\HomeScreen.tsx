import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  useColorScheme,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {
  FAB,
  Searchbar,
  Card,
  Title,
  Paragraph,
  IconButton,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { LiquidGlass } from '../components/LiquidGlass';

const { width } = Dimensions.get('window');
import { StackNavigationProp } from '@react-navigation/stack';

// Mock data for demo
const mockDocuments = [
  {
    id: '1',
    name: 'Passport',
    category: 'IDs',
    fileSize: 2048000,
    dateCreated: new Date('2024-01-15'),
    fileExtension: 'pdf',
  },
  {
    id: '2',
    name: 'Electricity Bill',
    category: 'Bills',
    fileSize: 1024000,
    dateCreated: new Date('2024-02-01'),
    fileExtension: 'pdf',
  },
  {
    id: '3',
    name: 'University Certificate',
    category: 'Certificates',
    fileSize: 3072000,
    dateCreated: new Date('2024-01-20'),
    fileExtension: 'pdf',
  },
  {
    id: '4',
    name: 'Meeting Notes',
    category: 'Personal Notes',
    fileSize: 512000,
    dateCreated: new Date('2024-02-10'),
    fileExtension: 'txt',
  },
];

type RootStackParamList = {
  Home: undefined;
  DocumentDetail: { documentId: string };
  DocumentImport: undefined;
};

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const isDarkMode = useColorScheme() === 'dark';
  const [documents] = useState(mockDocuments);
  const [filteredDocuments, setFilteredDocuments] = useState(mockDocuments);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const filtered = documents.filter(doc =>
        doc.name.toLowerCase().includes(query.toLowerCase()) ||
        doc.category.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredDocuments(filtered);
    } else {
      setFilteredDocuments(documents);
    }
  };

  const handleDocumentPress = (document: any) => {
    navigation.navigate('DocumentDetail', { documentId: document.id });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'IDs':
        return '🆔';
      case 'Bills':
        return '🧾';
      case 'Certificates':
        return '🏆';
      case 'Personal Notes':
        return '📝';
      default:
        return '📁';
    }
  };

  const renderDocumentCard = ({ item }: { item: any }) => (
    <Card
      style={styles.documentCard}
      onPress={() => handleDocumentPress(item)}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <View style={styles.cardInfo}>
            <View style={styles.titleRow}>
              <Text style={styles.categoryIcon}>{getCategoryIcon(item.category)}</Text>
              <Title style={styles.documentTitle} numberOfLines={1}>
                {item.name}
              </Title>
            </View>
            <Paragraph style={styles.documentCategory}>
              {item.category}
            </Paragraph>
            <Paragraph style={styles.documentSize}>
              {formatFileSize(item.fileSize)} • {item.fileExtension.toUpperCase()}
            </Paragraph>
            <Paragraph style={styles.documentDate}>
              {item.dateCreated.toLocaleDateString()}
            </Paragraph>
          </View>
          <IconButton
            icon="chevron-right"
            size={20}
            onPress={() => handleDocumentPress(item)}
          />
        </View>
      </Card.Content>
    </Card>
  );

  const styles = getStyles(isDarkMode);

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <Searchbar
        placeholder="Search documents..."
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchBar}
      />

      {/* Documents List */}
      {filteredDocuments.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📄</Text>
          <Text style={styles.emptyStateTitle}>
            {searchQuery ? 'No Documents Found' : 'Welcome to MyDocs Vault'}
          </Text>
          <Text style={styles.emptyStateDescription}>
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'This is a demo version with sample documents. Tap the + button to see the import screen.'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredDocuments}
          renderItem={renderDocumentCard}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.documentsList}
        />
      )}

      {/* Add Document FAB */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('DocumentImport')}
      />
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    },
    searchBar: {
      margin: 16,
      marginBottom: 8,
    },
    documentsList: {
      padding: 8,
    },
    documentCard: {
      margin: 8,
    },
    cardHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    cardInfo: {
      flex: 1,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    categoryIcon: {
      fontSize: 20,
      marginRight: 8,
    },
    documentTitle: {
      fontSize: 16,
      fontWeight: '600',
      flex: 1,
    },
    documentCategory: {
      fontSize: 14,
      color: isDarkMode ? '#cccccc' : '#666666',
      marginBottom: 2,
    },
    documentSize: {
      fontSize: 12,
      color: isDarkMode ? '#aaaaaa' : '#888888',
      marginBottom: 2,
    },
    documentDate: {
      fontSize: 12,
      color: isDarkMode ? '#aaaaaa' : '#888888',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyStateIcon: {
      fontSize: 64,
      marginBottom: 16,
    },
    emptyStateTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDarkMode ? '#ffffff' : '#000000',
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyStateDescription: {
      fontSize: 16,
      color: isDarkMode ? '#cccccc' : '#666666',
      textAlign: 'center',
      lineHeight: 22,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: 0,
      backgroundColor: '#007AFF',
    },
  });

export default HomeScreen;
