//
//  DocumentDetailView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI
import QuickLook
import PDFKit

struct DocumentDetailView: View {
    let document: Document
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var documentManager: DocumentManager
    @State private var showingQuickLook = false
    @State private var showingShareSheet = false
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Document Preview
                    DocumentPreviewSection(document: document)
                    
                    // Document Information
                    DocumentInfoSection(document: document)
                    
                    // Actions
                    DocumentActionsSection(
                        document: document,
                        showingQuickLook: $showingQuickLook,
                        showingShareSheet: $showingShareSheet,
                        showingDeleteAlert: $showingDeleteAlert
                    )
                }
                .padding()
            }
            .navigationTitle(document.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            showingQuickLook = true
                        }) {
                            Label("View Document", systemImage: "eye")
                        }
                        
                        Button(action: {
                            showingShareSheet = true
                        }) {
                            Label("Share", systemImage: "square.and.arrow.up")
                        }
                        
                        Divider()
                        
                        Button(role: .destructive, action: {
                            showingDeleteAlert = true
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingQuickLook) {
                QuickLookView(url: documentManager.getDocumentURL(for: document))
            }
            .sheet(isPresented: $showingShareSheet) {
                ShareSheet(items: [documentManager.getDocumentURL(for: document)])
            }
            .alert("Delete Document", isPresented: $showingDeleteAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    documentManager.deleteDocument(document)
                    dismiss()
                }
            } message: {
                Text("Are you sure you want to delete '\(document.name)'? This action cannot be undone.")
            }
        }
    }
}

struct DocumentPreviewSection: View {
    let document: Document
    @EnvironmentObject var documentManager: DocumentManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Preview")
                .font(.headline)
            
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
                    .frame(height: 200)
                
                if document.isPDF {
                    PDFPreviewView(url: documentManager.getDocumentURL(for: document))
                        .frame(height: 200)
                        .cornerRadius(16)
                } else if let thumbnailImage = documentManager.getThumbnailImage(for: document) {
                    Image(uiImage: thumbnailImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 200)
                        .cornerRadius(16)
                } else {
                    VStack {
                        Image(systemName: document.systemImageName)
                            .font(.system(size: 50))
                            .foregroundColor(document.category.color)
                        
                        Text(document.fileExtension.uppercased())
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
}

struct DocumentInfoSection: View {
    let document: Document
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Information")
                .font(.headline)
            
            VStack(spacing: 12) {
                InfoRow(label: "Name", value: document.name)
                InfoRow(label: "Category", value: document.category.displayName, icon: document.category.systemImageName, iconColor: document.category.color)
                InfoRow(label: "File Size", value: document.formattedFileSize)
                InfoRow(label: "File Type", value: document.fileExtension.uppercased())
                InfoRow(label: "Created", value: DateFormatter.mediumDate.string(from: document.dateCreated))
                InfoRow(label: "Modified", value: DateFormatter.mediumDate.string(from: document.dateModified))
            }
        }
    }
}

struct DocumentActionsSection: View {
    let document: Document
    @Binding var showingQuickLook: Bool
    @Binding var showingShareSheet: Bool
    @Binding var showingDeleteAlert: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Actions")
                .font(.headline)
            
            VStack(spacing: 12) {
                ActionButton(
                    title: "View Document",
                    icon: "eye.fill",
                    color: .blue
                ) {
                    showingQuickLook = true
                }
                
                ActionButton(
                    title: "Share Document",
                    icon: "square.and.arrow.up.fill",
                    color: .green
                ) {
                    showingShareSheet = true
                }
                
                ActionButton(
                    title: "Delete Document",
                    icon: "trash.fill",
                    color: .red
                ) {
                    showingDeleteAlert = true
                }
            }
        }
    }
}

struct InfoRow: View {
    let label: String
    let value: String
    var icon: String? = nil
    var iconColor: Color? = nil
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            if let icon = icon {
                Image(systemName: icon)
                    .foregroundColor(iconColor ?? .primary)
                    .font(.caption)
            }
            
            Text(value)
                .font(.subheadline)
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .frame(width: 24)
                
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

extension DateFormatter {
    static let mediumDate: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}

#Preview {
    DocumentDetailView(document: Document(
        name: "Sample Document",
        fileName: "sample.pdf",
        fileExtension: "pdf",
        category: .certificates,
        filePath: "/path/to/file",
        fileSize: 1024000
    ))
    .environmentObject(DocumentManager())
}
