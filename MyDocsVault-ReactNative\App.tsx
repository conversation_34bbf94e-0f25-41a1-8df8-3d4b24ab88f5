import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {Provider as PaperProvider} from 'react-native-paper';
import {GestureHandlerRootView} from 'react-native-gesture-handler';

// Screens
import AuthenticationScreen from './src/screens/AuthenticationScreen';
import HomeScreen from './src/screens/HomeScreen';
import DocumentDetailScreen from './src/screens/DocumentDetailScreen';
import DocumentImportScreen from './src/screens/DocumentImportScreen';
import CategoryScreen from './src/screens/CategoryScreen';

// Services
import {AuthenticationService} from './src/services/AuthenticationService';
import {DocumentService} from './src/services/DocumentService';

// Types
export type RootStackParamList = {
  Authentication: undefined;
  Home: undefined;
  DocumentDetail: {documentId: string};
  DocumentImport: undefined;
  Category: {category: string};
};

const Stack = createStackNavigator<RootStackParamList>();

const App: React.FC = () => {
  const isDarkMode = useColorScheme() === 'dark';
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize services
      await DocumentService.initialize();
      
      // Check if biometric authentication is available
      const biometricAvailable = await AuthenticationService.isBiometricAvailable();
      
      if (biometricAvailable) {
        // Attempt automatic authentication
        const authResult = await AuthenticationService.authenticate();
        setIsAuthenticated(authResult.success);
      } else {
        // Skip authentication if biometrics not available (for testing)
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('App initialization error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAuthenticationSuccess = () => {
    setIsAuthenticated(true);
  };

  if (isLoading) {
    return null; // You can add a loading screen here
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <PaperProvider>
        <SafeAreaView style={styles.container}>
          <StatusBar
            barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            backgroundColor={isDarkMode ? '#000000' : '#FFFFFF'}
          />
          <NavigationContainer>
            <Stack.Navigator
              initialRouteName={isAuthenticated ? 'Home' : 'Authentication'}
              screenOptions={{
                headerStyle: {
                  backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
                },
                headerTintColor: isDarkMode ? '#ffffff' : '#000000',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}>
              {!isAuthenticated ? (
                <Stack.Screen
                  name="Authentication"
                  options={{headerShown: false}}>
                  {props => (
                    <AuthenticationScreen
                      {...props}
                      onAuthenticationSuccess={handleAuthenticationSuccess}
                    />
                  )}
                </Stack.Screen>
              ) : (
                <>
                  <Stack.Screen
                    name="Home"
                    component={HomeScreen}
                    options={{
                      title: 'MyDocs Vault',
                      headerStyle: {
                        backgroundColor: '#007AFF',
                      },
                      headerTintColor: '#ffffff',
                    }}
                  />
                  <Stack.Screen
                    name="DocumentDetail"
                    component={DocumentDetailScreen}
                    options={{title: 'Document Details'}}
                  />
                  <Stack.Screen
                    name="DocumentImport"
                    component={DocumentImportScreen}
                    options={{title: 'Import Document'}}
                  />
                  <Stack.Screen
                    name="Category"
                    component={CategoryScreen}
                    options={({route}) => ({
                      title: `${route.params.category} Documents`,
                    })}
                  />
                </>
              )}
            </Stack.Navigator>
          </NavigationContainer>
        </SafeAreaView>
      </PaperProvider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
