//
//  DocumentManager.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import Foundation
import SwiftUI
import UIKit
import PDFKit

@MainActor
class DocumentManager: ObservableObject {
    @Published var documents: [Document] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    private let thumbnailsDirectory: URL
    private let metadataFileName = "documents_metadata.json"
    
    init() {
        // Create secure documents directory in app's Documents folder
        let appDocumentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        documentsDirectory = appDocumentsPath.appendingPathComponent("SecureDocuments")
        thumbnailsDirectory = documentsDirectory.appendingPathComponent("Thumbnails")
        
        createDirectoriesIfNeeded()
        loadDocuments()
    }
    
    private func createDirectoriesIfNeeded() {
        do {
            try fileManager.createDirectory(at: documentsDirectory, withIntermediateDirectories: true)
            try fileManager.createDirectory(at: thumbnailsDirectory, withIntermediateDirectories: true)
        } catch {
            errorMessage = "Failed to create directories: \(error.localizedDescription)"
        }
    }
    
    func loadDocuments() {
        isLoading = true
        
        Task {
            do {
                let metadataURL = documentsDirectory.appendingPathComponent(metadataFileName)
                
                if fileManager.fileExists(atPath: metadataURL.path) {
                    let data = try Data(contentsOf: metadataURL)
                    let loadedDocuments = try JSONDecoder().decode([Document].self, from: data)
                    
                    // Verify files still exist and update documents list
                    documents = loadedDocuments.filter { document in
                        let filePath = documentsDirectory.appendingPathComponent(document.fileName)
                        return fileManager.fileExists(atPath: filePath.path)
                    }
                } else {
                    documents = []
                }
                
                isLoading = false
            } catch {
                errorMessage = "Failed to load documents: \(error.localizedDescription)"
                isLoading = false
            }
        }
    }
    
    private func saveMetadata() {
        do {
            let data = try JSONEncoder().encode(documents)
            let metadataURL = documentsDirectory.appendingPathComponent(metadataFileName)
            try data.write(to: metadataURL)
        } catch {
            errorMessage = "Failed to save metadata: \(error.localizedDescription)"
        }
    }
    
    func addDocument(from sourceURL: URL, name: String, category: DocumentCategory) async -> Bool {
        do {
            let fileExtension = sourceURL.pathExtension
            let fileName = "\(UUID().uuidString).\(fileExtension)"
            let destinationURL = documentsDirectory.appendingPathComponent(fileName)
            
            // Copy file to secure location
            try fileManager.copyItem(at: sourceURL, to: destinationURL)
            
            // Get file size
            let attributes = try fileManager.attributesOfItem(atPath: destinationURL.path)
            let fileSize = attributes[.size] as? Int64 ?? 0
            
            // Create document object
            var document = Document(
                name: name,
                fileName: fileName,
                fileExtension: fileExtension,
                category: category,
                filePath: destinationURL.path,
                fileSize: fileSize
            )
            
            // Generate thumbnail if it's an image or PDF
            if let thumbnailPath = await generateThumbnail(for: destinationURL, documentId: document.id) {
                document.thumbnailPath = thumbnailPath
            }
            
            documents.append(document)
            saveMetadata()
            
            return true
        } catch {
            errorMessage = "Failed to add document: \(error.localizedDescription)"
            return false
        }
    }
    
    func deleteDocument(_ document: Document) {
        do {
            // Remove file
            let fileURL = URL(fileURLWithPath: document.filePath)
            try fileManager.removeItem(at: fileURL)
            
            // Remove thumbnail if exists
            if let thumbnailPath = document.thumbnailPath {
                let thumbnailURL = URL(fileURLWithPath: thumbnailPath)
                try? fileManager.removeItem(at: thumbnailURL)
            }
            
            // Remove from documents array
            documents.removeAll { $0.id == document.id }
            saveMetadata()
            
        } catch {
            errorMessage = "Failed to delete document: \(error.localizedDescription)"
        }
    }
    
    private func generateThumbnail(for fileURL: URL, documentId: UUID) async -> String? {
        let thumbnailURL = thumbnailsDirectory.appendingPathComponent("\(documentId.uuidString).jpg")
        
        do {
            if fileURL.pathExtension.lowercased() == "pdf" {
                // Generate PDF thumbnail
                guard let pdfDocument = PDFDocument(url: fileURL),
                      let firstPage = pdfDocument.page(at: 0) else {
                    return nil
                }
                
                let pageRect = firstPage.bounds(for: .mediaBox)
                let renderer = UIGraphicsImageRenderer(size: CGSize(width: 200, height: 200 * pageRect.height / pageRect.width))
                
                let image = renderer.image { context in
                    UIColor.white.set()
                    context.fill(CGRect(origin: .zero, size: renderer.format.bounds.size))
                    
                    context.cgContext.translateBy(x: 0, y: renderer.format.bounds.height)
                    context.cgContext.scaleBy(x: 1, y: -1)
                    context.cgContext.scaleBy(x: renderer.format.bounds.width / pageRect.width,
                                            y: renderer.format.bounds.height / pageRect.height)
                    
                    firstPage.draw(with: .mediaBox, to: context.cgContext)
                }
                
                if let imageData = image.jpegData(compressionQuality: 0.8) {
                    try imageData.write(to: thumbnailURL)
                    return thumbnailURL.path
                }
            } else {
                // Generate image thumbnail
                guard let image = UIImage(contentsOfFile: fileURL.path) else {
                    return nil
                }
                
                let thumbnailImage = image.preparingThumbnail(of: CGSize(width: 200, height: 200))
                
                if let imageData = thumbnailImage?.jpegData(compressionQuality: 0.8) {
                    try imageData.write(to: thumbnailURL)
                    return thumbnailURL.path
                }
            }
        } catch {
            print("Failed to generate thumbnail: \(error)")
        }
        
        return nil
    }
    
    func getDocumentURL(for document: Document) -> URL {
        return URL(fileURLWithPath: document.filePath)
    }
    
    func getThumbnailImage(for document: Document) -> UIImage? {
        guard let thumbnailPath = document.thumbnailPath else { return nil }
        return UIImage(contentsOfFile: thumbnailPath)
    }
    
    func searchDocuments(query: String) -> [Document] {
        if query.isEmpty {
            return documents
        }
        
        return documents.filter { document in
            document.name.localizedCaseInsensitiveContains(query) ||
            document.category.displayName.localizedCaseInsensitiveContains(query)
        }
    }
    
    func documentsInCategory(_ category: DocumentCategory) -> [Document] {
        return documents.filter { $0.category == category }
    }
}
