# Quick Setup Guide for MyDocs Vault Apps

## Prerequisites Check
✅ Node.js v22.15.0 - Installed
✅ npm v11.4.2 - Installed
❌ Android Studio - Not installed
❌ Flutter SDK - Not installed
❌ Java JDK - Unknown

## Step 1: Install Java JDK 17
1. Download JDK 17 from: https://adoptium.net/temurin/releases/
2. Install and add to PATH
3. Verify: `java -version`

## Step 2: Install Android Studio
1. Download from: https://developer.android.com/studio
2. Install with default settings
3. Open Android Studio and install:
   - Android SDK Platform 34
   - Android SDK Build-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM)

## Step 3: Set Environment Variables
Add these to your Windows Environment Variables:
```
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
JAVA_HOME = C:\Program Files\Eclipse Adoptium\jdk-17.0.x-hotspot
```

Add to PATH:
```
%ANDROID_HOME%\platform-tools
%ANDROID_HOME%\tools
%ANDROID_HOME%\tools\bin
%JAVA_HOME%\bin
```

## Step 4: Install Flutter (Optional - for Flutter version)
1. Download Flutter SDK: https://docs.flutter.dev/get-started/install/windows
2. Extract to C:\flutter
3. Add C:\flutter\bin to PATH
4. Run: `flutter doctor`

## Step 5: Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to Tools > AVD Manager
3. Create Virtual Device
4. Choose Pixel 7 or similar
5. Download and select Android 14 (API 34)
6. Finish setup

## Step 6: Run the Apps

### For React Native:
```bash
cd MyDocsVault-ReactNative
npm install
npx react-native run-android
```

### For Flutter:
```bash
cd MyDocsVault-Flutter
flutter pub get
flutter run
```

## Troubleshooting
- If you get "adb not found", restart your terminal after setting environment variables
- If emulator doesn't start, enable Hyper-V in Windows Features
- For permission errors, run terminal as Administrator

## Current Status
- React Native project: Dependencies installing...
- Flutter project: Ready for setup after Flutter SDK installation

## Next Steps
1. Install Android Studio first
2. Set up environment variables
3. Create an Android emulator
4. Run the React Native app (it's more complete)
5. Install Flutter SDK to run the Flutter version
