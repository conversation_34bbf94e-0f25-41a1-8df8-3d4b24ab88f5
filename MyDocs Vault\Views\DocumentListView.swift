//
//  DocumentListView.swift
//  MyDocs Vault
//
//  Created on 2025-06-27.
//

import SwiftUI

struct DocumentListView: View {
    @EnvironmentObject var documentManager: DocumentManager
    @EnvironmentObject var authManager: AuthenticationManager
    @State private var searchText = ""
    @State private var selectedCategory: DocumentCategory? = nil
    @State private var showingImportView = false
    @State private var showingCategoryFilter = false
    @State private var isGridView = false
    
    private var filteredDocuments: [Document] {
        var documents = documentManager.searchDocuments(query: searchText)
        
        if let category = selectedCategory {
            documents = documents.filter { $0.category == category }
        }
        
        return documents.sorted { $0.dateModified > $1.dateModified }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if documentManager.isLoading {
                    ProgressView("Loading documents...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if filteredDocuments.isEmpty {
                    EmptyStateView(showingImportView: $showingImportView)
                } else {
                    DocumentGridView(documents: filteredDocuments, isGridView: isGridView)
                }
            }
            .navigationTitle("My Documents")
            .navigationBarTitleDisplayMode(.large)
            .searchable(text: $searchText, prompt: "Search documents...")
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    Button(action: {
                        isGridView.toggle()
                    }) {
                        Image(systemName: isGridView ? "list.bullet" : "square.grid.2x2")
                    }
                    
                    Button(action: {
                        showingCategoryFilter.toggle()
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .foregroundColor(selectedCategory != nil ? .blue : .primary)
                    }
                    
                    Button(action: {
                        showingImportView = true
                    }) {
                        Image(systemName: "plus")
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Logout") {
                        authManager.logout()
                    }
                    .foregroundColor(.red)
                }
            }
            .sheet(isPresented: $showingImportView) {
                DocumentImportView()
            }
            .actionSheet(isPresented: $showingCategoryFilter) {
                ActionSheet(
                    title: Text("Filter by Category"),
                    buttons: categoryFilterButtons()
                )
            }
            .alert("Error", isPresented: .constant(documentManager.errorMessage != nil)) {
                Button("OK") {
                    documentManager.errorMessage = nil
                }
            } message: {
                Text(documentManager.errorMessage ?? "")
            }
        }
    }
    
    private func categoryFilterButtons() -> [ActionSheet.Button] {
        var buttons: [ActionSheet.Button] = []
        
        // Add "All" option
        buttons.append(.default(Text("All Documents")) {
            selectedCategory = nil
        })
        
        // Add category options
        for category in DocumentCategory.allCases {
            let count = documentManager.documentsInCategory(category).count
            buttons.append(.default(Text("\(category.displayName) (\(count))")) {
                selectedCategory = category
            })
        }
        
        buttons.append(.cancel())
        return buttons
    }
}

struct EmptyStateView: View {
    @Binding var showingImportView: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Documents Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Start by adding your first document")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                showingImportView = true
            }) {
                HStack {
                    Image(systemName: "plus")
                    Text("Add Document")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
        .padding()
    }
}

struct DocumentGridView: View {
    let documents: [Document]
    let isGridView: Bool
    @EnvironmentObject var documentManager: DocumentManager
    @State private var selectedDocument: Document?
    
    private let columns = [
        GridItem(.adaptive(minimum: 150), spacing: 16)
    ]
    
    var body: some View {
        ScrollView {
            if isGridView {
                LazyVGrid(columns: columns, spacing: 16) {
                    ForEach(documents) { document in
                        DocumentCardView(document: document)
                            .onTapGesture {
                                selectedDocument = document
                            }
                    }
                }
                .padding()
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(documents) { document in
                        DocumentRowView(document: document)
                            .onTapGesture {
                                selectedDocument = document
                            }
                    }
                }
                .padding(.horizontal)
            }
        }
        .sheet(item: $selectedDocument) { document in
            DocumentDetailView(document: document)
        }
    }
}

#Preview {
    DocumentListView()
        .environmentObject(DocumentManager())
        .environmentObject(AuthenticationManager())
}
